# ChatMuse Makefile

.PHONY: help build run test clean docker-up docker-down docker-build docker-logs migrate dev hot

# Default target
help:
	@echo "ChatMuse - Available commands:"
	@echo "  make dev          - Run in development mode"
	@echo "  make build        - Build the application"
	@echo "  make run          - Run the application"
	@echo "  make test         - Run tests"
	@echo "  make clean        - Clean build artifacts"
	@echo "  make docker-up    - Start Docker containers"
	@echo "  make docker-down  - Stop Docker containers"
	@echo "  make docker-build - Build Docker image"
	@echo "  make docker-logs  - Show Docker logs"
	@echo "  make hot          - Hot reload with Docker"
	@echo "  make migrate      - Run database migrations"
	@echo "  make deps         - Install dependencies"
	@echo "  make swagger      - Generate Swagger docs"

# Development
dev:
	@echo "Starting ChatMuse in development mode..."
	go run main.go

# Hot reload with Docker
hot:
	@echo "Starting ChatMuse with hot reload..."
	docker-compose -f docker-compose.yml up --build

# Build
build:
	@echo "Building ChatMuse..."
	go build -o bin/chatmuse main.go

# Run
run: build
	@echo "Running ChatMuse..."
	./bin/chatmuse

# Test
test:
	@echo "Running tests..."
	go test -v ./...

# Clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	go clean

# Dependencies
deps:
	@echo "Installing dependencies..."
	go mod tidy
	go mod download

# Swagger documentation
swagger:
	@echo "Generating Swagger documentation..."
	swag init -g main.go

# Docker commands
docker-up:
	@echo "Starting Docker containers..."
	docker-compose up -d

docker-down:
	@echo "Stopping Docker containers..."
	docker-compose down

docker-build:
	@echo "Building Docker image..."
	docker-compose build

docker-logs:
	@echo "Showing Docker logs..."
	docker-compose logs -f

docker-restart: docker-down docker-up

# Database
migrate:
	@echo "Running database migrations..."
	@echo "Migrations are handled automatically by GORM on startup"

# Database management
db-reset:
	@echo "Resetting database..."
	docker-compose down -v
	docker-compose up -d saye-db
	sleep 5
	docker-compose up -d

# Quick setup for new developers
setup: deps docker-up
	@echo "Setting up ChatMuse development environment..."
	@echo "Waiting for database to be ready..."
	sleep 10
	@echo "Setup complete! Run 'make dev' to start development server"

# Health check
health:
	@echo "Checking application health..."
	curl -f http://localhost:8000/api/v1/version || echo "Application not running"

# Database shell
db-shell:
	@echo "Connecting to database..."
	docker exec -it chatmuse-db psql -U chatmuse_user -d chatmuse_db

# Redis shell
redis-shell:
	@echo "Connecting to Redis..."
	docker exec -it chatmuse-redis redis-cli -a chatmuse_redis_pass