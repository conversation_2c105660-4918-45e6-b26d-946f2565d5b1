#!/bin/bash

# JWT Authentication Test Script
BASE_URL="http://localhost:8000/api/v1"

echo "🔐 Testing JWT Authentication..."

# Step 1: Login and get token
echo "1️⃣ Login and get JWT token..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}')

echo "Login Response:"
echo "$LOGIN_RESPONSE" | jq . 2>/dev/null || echo "$LOGIN_RESPONSE"

# Extract token
if command -v jq >/dev/null 2>&1; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.token // empty')
    if [ -n "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
        echo "✅ Token obtained: ${TOKEN:0:50}..."
    else
        echo "❌ Failed to get token"
        exit 1
    fi
else
    echo "❌ jq not available, cannot extract token"
    exit 1
fi

echo ""

# Step 2: Test auth endpoint
echo "2️⃣ Testing auth test endpoint..."
AUTH_TEST_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/test" \
  -H "Authorization: Bearer $TOKEN")

echo "Auth Test Response:"
echo "$AUTH_TEST_RESPONSE" | jq . 2>/dev/null || echo "$AUTH_TEST_RESPONSE"

echo ""

# Step 3: Test chat endpoint
echo "3️⃣ Testing chat endpoint..."
CHAT_RESPONSE=$(curl -s -X GET "$BASE_URL/chats" \
  -H "Authorization: Bearer $TOKEN")

echo "Chat Response:"
echo "$CHAT_RESPONSE" | jq . 2>/dev/null || echo "$CHAT_RESPONSE"

echo ""

# Step 4: Test without token (should fail)
echo "4️⃣ Testing without token (should fail)..."
NO_TOKEN_RESPONSE=$(curl -s -X GET "$BASE_URL/chats")

echo "No Token Response:"
echo "$NO_TOKEN_RESPONSE" | jq . 2>/dev/null || echo "$NO_TOKEN_RESPONSE"

echo ""
echo "🎉 JWT Authentication test completed!"
