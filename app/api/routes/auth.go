package routes

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-base-project-structure/pkg/domains/auth"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/middleware"
	"github.com/google/uuid"
)

type AuthHandler struct {
	authService auth.Service
}

func NewAuthHandler(authService auth.Service) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

func (h *AuthHandler) RegisterRoutes(router *gin.RouterGroup) {
	authRoutes := router.Group("/auth")
	{
		// Public routes (no authentication required)
		authRoutes.POST("/register", h.Register)
		authRoutes.POST("/login", h.Login)
	}

	// Protected routes (authentication required)
	protectedRoutes := router.Group("/auth")
	protectedRoutes.Use(middleware.JWTAuth())
	{
		protectedRoutes.GET("/profile", h.GetProfile)
		protectedRoutes.PUT("/profile", h.UpdateProfile)
		protectedRoutes.POST("/change-password", h.ChangePassword)
		protectedRoutes.POST("/logout", h.Logout)
		protectedRoutes.GET("/test", h.TestAuth) // Debug endpoint
	}
}

// @Summary Register new user
// @Description Register a new user account
// @Tags auth
// @Accept json
// @Produce json
// @Param user body dtos.CreateUserReqDto true "User registration data"
// @Success 201 {object} dtos.CreateUserRespDto
// @Failure 400 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req dtos.CreateUserReqDto
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	response, err := h.authService.Register(req)
	if err != nil {
		if err.Error() == "user with this email or username already exists" {
			c.JSON(http.StatusConflict, gin.H{
				"error": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// @Summary User login
// @Description Authenticate user and get JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param credentials body dtos.AuthenticationRequest true "Login credentials"
// @Success 200 {object} dtos.AuthenticationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req dtos.AuthenticationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	response, err := h.authService.Login(&req)
	if err != nil {
		if err.Error() == "invalid credentials" || err.Error() == "email or username is required" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Get user profile
// @Description Get current user's profile information
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dtos.UserProfileResponse
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.authService.GetUserProfile(userID.(uuid.UUID))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Update user profile
// @Description Update current user's profile information
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param profile body dtos.UpdateProfileRequest true "Profile update data"
// @Success 200 {object} dtos.UpdateProfileResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 409 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/auth/profile [put]
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req dtos.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	response, err := h.authService.UpdateProfile(userID.(uuid.UUID), req)
	if err != nil {
		if err.Error() == "email is already taken" {
			c.JSON(http.StatusConflict, gin.H{
				"error": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Change password
// @Description Change current user's password
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param password body dtos.ChangePasswordRequest true "Password change data"
// @Success 200 {object} dtos.ChangePasswordResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req dtos.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	response, err := h.authService.ChangePassword(userID.(uuid.UUID), req)
	if err != nil {
		if err.Error() == "current password is incorrect" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Logout user
// @Description Logout current user (client-side token removal)
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// In a stateless JWT system, logout is typically handled client-side
	// by removing the token. Here we just return a success message.
	// In a more complex system, you might maintain a blacklist of tokens.

	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
		"note":    "Please remove the token from client storage",
	})
}

// @Summary Test authentication
// @Description Test endpoint to verify JWT authentication and user context
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/test [get]
func (h *AuthHandler) TestAuth(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get all context values for debugging
	currentUserID, _ := c.Get("current_user_id")
	currentUserIP, _ := c.Get("current_user_ip")
	currentDeviceID, _ := c.Get("current_device_id")
	currentTimezone, _ := c.Get("current_timezone")
	currentPhoneLanguage, _ := c.Get("current_phone_language")

	c.JSON(http.StatusOK, gin.H{
		"message":      "Authentication successful",
		"user_id":      userID,
		"user_id_type": fmt.Sprintf("%T", userID),
		"context": gin.H{
			"current_user_id":        currentUserID,
			"current_user_ip":        currentUserIP,
			"current_device_id":      currentDeviceID,
			"current_timezone":       currentTimezone,
			"current_phone_language": currentPhoneLanguage,
		},
	})
}
