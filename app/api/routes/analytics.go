package routes

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-base-project-structure/pkg/domains/analytics"
	"github.com/go-base-project-structure/pkg/domains/social"
	"github.com/go-base-project-structure/pkg/domains/wrapped"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/go-base-project-structure/pkg/middleware"
	"github.com/google/uuid"
)

type AnalyticsHandler struct {
	analyticsService analytics.Service
	socialService    social.Service
	wrappedService   wrapped.Service
}

func NewAnalyticsHandler(analyticsService analytics.Service, socialService social.Service, wrappedService wrapped.Service) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
		socialService:    socialService,
		wrappedService:   wrappedService,
	}
}

func (h *AnalyticsHandler) RegisterRoutes(router *gin.RouterGroup) {
	analyticsRoutes := router.Group("/analytics")
	analyticsRoutes.Use(middleware.JWTAuth()) // Require JWT authentication for all analytics routes
	{
		// Chat analysis
		analyticsRoutes.POST("/chats/:chatId/analyze", h.AnalyzeChat)
		analyticsRoutes.GET("/chats/:chatId", h.GetChatAnalytics)
		analyticsRoutes.GET("/chats/:chatId/insights", h.GetChatInsights)

		// User analytics
		analyticsRoutes.GET("/user", h.GetUserAnalytics)
		analyticsRoutes.GET("/user/emojis", h.GetUserEmojiAnalysis)
		analyticsRoutes.GET("/user/words", h.GetUserWordAnalysis)

		// Wrapped summaries
		analyticsRoutes.POST("/wrapped/:year", h.GenerateWrappedSummary)
		analyticsRoutes.GET("/wrapped", h.GetWrappedHistory)
		analyticsRoutes.GET("/wrapped/:year", h.GetWrappedSummary)
		analyticsRoutes.POST("/wrapped/:year/:month", h.GenerateMonthlyWrapped)

		// Social network analysis
		analyticsRoutes.GET("/social", h.GetSocialNetwork)
		analyticsRoutes.POST("/social/analyze", h.AnalyzeSocialNetwork)
		analyticsRoutes.GET("/social/strongest", h.GetStrongestConnections)
		analyticsRoutes.GET("/social/weakest", h.GetWeakestConnections)
		analyticsRoutes.GET("/social/trends", h.GetRelationshipTrends)
		analyticsRoutes.GET("/social/contacts/:contactId", h.GetContactRelationship)
	}
}

// @Summary Analyze chat
// @Description Analyze a specific chat and generate insights
// @Tags analytics
// @Produce json
// @Param chatId path string true "Chat ID"
// @Success 200 {object} dtos.AnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/chats/{chatId}/analyze [post]
func (h *AnalyticsHandler) AnalyzeChat(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	chatIDStr := c.Param("chatId")
	chatID, err := uuid.Parse(chatIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
		return
	}

	analysis, err := h.analyticsService.AnalyzeChat(userID.(uuid.UUID), chatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response := h.convertAnalysisToResponse(analysis)
	c.JSON(http.StatusOK, response)
}

// @Summary Get chat analytics
// @Description Get existing analytics for a specific chat
// @Tags analytics
// @Produce json
// @Param chatId path string true "Chat ID"
// @Success 200 {object} dtos.AnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Router /api/analytics/chats/{chatId} [get]
func (h *AnalyticsHandler) GetChatAnalytics(c *gin.Context) {
	chatIDStr := c.Param("chatId")
	chatID, err := uuid.Parse(chatIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
		return
	}

	analysis, err := h.analyticsService.GetChatAnalytics(chatID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Analytics not found"})
		return
	}

	response := h.convertAnalysisToResponse(analysis)
	c.JSON(http.StatusOK, response)
}

// @Summary Get chat insights
// @Description Get detailed insights for a specific chat
// @Tags analytics
// @Produce json
// @Param chatId path string true "Chat ID"
// @Success 200 {object} dtos.ChatInsightsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/chats/{chatId}/insights [get]
func (h *AnalyticsHandler) GetChatInsights(c *gin.Context) {
	chatIDStr := c.Param("chatId")
	chatID, err := uuid.Parse(chatIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
		return
	}

	insights, err := h.analyticsService.GetChatInsights(chatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, insights)
}

// @Summary Get user analytics
// @Description Get analytics for the authenticated user
// @Tags analytics
// @Produce json
// @Param type query string false "Analysis type" Enums(daily,weekly,monthly,yearly,wrapped)
// @Success 200 {object} dtos.AnalyticsListResponse
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/user [get]
func (h *AnalyticsHandler) GetUserAnalytics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	analysisTypeStr := c.Query("type")
	var analysisType entities.AnalysisType
	if analysisTypeStr != "" {
		analysisType = entities.AnalysisType(analysisTypeStr)
	}

	analyses, err := h.analyticsService.GetUserAnalytics(userID.(uuid.UUID), analysisType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var responses []dtos.AnalyticsResponse
	for _, analysis := range analyses {
		responses = append(responses, h.convertAnalysisToResponse(&analysis))
	}

	c.JSON(http.StatusOK, dtos.AnalyticsListResponse{
		Analyses: responses,
		Total:    len(responses),
	})
}

// @Summary Generate wrapped summary
// @Description Generate a yearly wrapped summary for the user
// @Tags analytics
// @Produce json
// @Param year path int true "Year"
// @Success 200 {object} dtos.WrappedSummaryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/wrapped/{year} [post]
func (h *AnalyticsHandler) GenerateWrappedSummary(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	yearStr := c.Param("year")
	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 2020 || year > 2030 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid year"})
		return
	}

	wrapped, err := h.wrappedService.GenerateWrappedSummary(userID.(uuid.UUID), year)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, wrapped)
}

// @Summary Get wrapped history
// @Description Get all wrapped summaries for the user
// @Tags analytics
// @Produce json
// @Success 200 {array} dtos.WrappedSummaryResponse
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/wrapped [get]
func (h *AnalyticsHandler) GetWrappedHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	history, err := h.wrappedService.GetWrappedHistory(userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, history)
}

// @Summary Get wrapped summary
// @Description Get wrapped summary for a specific year
// @Tags analytics
// @Produce json
// @Param year path int true "Year"
// @Success 200 {object} dtos.WrappedSummaryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Router /api/analytics/wrapped/{year} [get]
func (h *AnalyticsHandler) GetWrappedSummary(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	yearStr := c.Param("year")
	year, err := strconv.Atoi(yearStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid year"})
		return
	}

	history, err := h.wrappedService.GetWrappedHistory(userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	for _, wrapped := range history {
		if wrapped.Year == year {
			c.JSON(http.StatusOK, wrapped)
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{"error": "Wrapped summary not found for this year"})
}

// @Summary Generate monthly wrapped
// @Description Generate a monthly wrapped summary
// @Tags analytics
// @Produce json
// @Param year path int true "Year"
// @Param month path int true "Month"
// @Success 200 {object} dtos.WrappedSummaryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/wrapped/{year}/{month} [post]
func (h *AnalyticsHandler) GenerateMonthlyWrapped(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	yearStr := c.Param("year")
	monthStr := c.Param("month")

	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 2020 || year > 2030 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid year"})
		return
	}

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid month"})
		return
	}

	wrapped, err := h.wrappedService.GenerateMonthlyWrapped(userID.(uuid.UUID), year, month)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, wrapped)
}

// @Summary Analyze social network
// @Description Analyze user's social network and relationships
// @Tags analytics
// @Produce json
// @Success 200 {object} dtos.SocialNetworkResponse
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/social/analyze [post]
func (h *AnalyticsHandler) AnalyzeSocialNetwork(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	network, err := h.socialService.AnalyzeSocialNetwork(userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, network)
}

// @Summary Get social network
// @Description Get existing social network analysis
// @Tags analytics
// @Produce json
// @Success 200 {object} dtos.SocialNetworkResponse
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/social [get]
func (h *AnalyticsHandler) GetSocialNetwork(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	network, err := h.socialService.AnalyzeSocialNetwork(userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, network)
}

// @Summary Get strongest connections
// @Description Get user's strongest relationships
// @Tags analytics
// @Produce json
// @Param limit query int false "Number of connections to return" default(10)
// @Success 200 {array} dtos.SocialNetworkNode
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/social/strongest [get]
func (h *AnalyticsHandler) GetStrongestConnections(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	connections, err := h.socialService.GetStrongestConnections(userID.(uuid.UUID), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, connections)
}

// @Summary Get weakest connections
// @Description Get user's weakest relationships
// @Tags analytics
// @Produce json
// @Param limit query int false "Number of connections to return" default(10)
// @Success 200 {array} dtos.SocialNetworkNode
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/social/weakest [get]
func (h *AnalyticsHandler) GetWeakestConnections(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	connections, err := h.socialService.GetWeakestConnections(userID.(uuid.UUID), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, connections)
}

// @Summary Get relationship trends
// @Description Get trends in user's relationships
// @Tags analytics
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/social/trends [get]
func (h *AnalyticsHandler) GetRelationshipTrends(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	trends, err := h.socialService.GetRelationshipTrends(userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, trends)
}

// @Summary Get contact relationship
// @Description Get relationship details for a specific contact
// @Tags analytics
// @Produce json
// @Param contactId path string true "Contact ID"
// @Success 200 {object} dtos.SocialNetworkNode
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Router /api/analytics/social/contacts/{contactId} [get]
func (h *AnalyticsHandler) GetContactRelationship(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	contactIDStr := c.Param("contactId")
	contactID, err := uuid.Parse(contactIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid contact ID"})
		return
	}

	relationship, err := h.socialService.GetContactRelationship(userID.(uuid.UUID), contactID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Relationship not found"})
		return
	}

	c.JSON(http.StatusOK, relationship)
}

// @Summary Get user emoji analysis
// @Description Get emoji usage analysis for the user
// @Tags analytics
// @Produce json
// @Param chat_id query string false "Chat ID to filter by"
// @Success 200 {object} dtos.EmojiAnalysisResponse
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/user/emojis [get]
func (h *AnalyticsHandler) GetUserEmojiAnalysis(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	chatIDStr := c.Query("chat_id")
	var chatID uuid.UUID
	var err error

	if chatIDStr != "" {
		chatID, err = uuid.Parse(chatIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
			return
		}
	}

	emojis, err := h.analyticsService.AnalyzeEmojis(userID.(uuid.UUID), chatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response format
	var emojiStats []dtos.EmojiStat
	totalEmojis := 0
	for _, emoji := range emojis {
		emojiStats = append(emojiStats, dtos.EmojiStat{
			Emoji: emoji.Emoji,
			Count: emoji.Count,
		})
		totalEmojis += emoji.Count
	}

	response := dtos.EmojiAnalysisResponse{
		TotalEmojis:  totalEmojis,
		UniqueEmojis: len(emojiStats),
		TopEmojis:    emojiStats,
		GeneratedAt:  time.Now(),
	}

	if chatIDStr != "" {
		response.ChatID = &chatID
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Get user word analysis
// @Description Get word usage analysis for the user
// @Tags analytics
// @Produce json
// @Param chat_id query string false "Chat ID to filter by"
// @Success 200 {object} dtos.WordAnalysisResponse
// @Failure 500 {object} map[string]interface{}
// @Router /api/analytics/user/words [get]
func (h *AnalyticsHandler) GetUserWordAnalysis(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	chatIDStr := c.Query("chat_id")
	var chatID uuid.UUID
	var err error

	if chatIDStr != "" {
		chatID, err = uuid.Parse(chatIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
			return
		}
	}

	words, err := h.analyticsService.AnalyzeWords(userID.(uuid.UUID), chatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert to response format
	var wordStats []dtos.WordStat
	totalWords := 0
	for _, word := range words {
		wordStats = append(wordStats, dtos.WordStat{
			Word:  word.Word,
			Count: word.Count,
		})
		totalWords += word.Count
	}

	response := dtos.WordAnalysisResponse{
		TotalWords:  totalWords,
		UniqueWords: len(wordStats),
		TopWords:    wordStats,
		GeneratedAt: time.Now(),
	}

	if chatIDStr != "" {
		response.ChatID = &chatID
	}

	c.JSON(http.StatusOK, response)
}

// Helper function to convert analysis entity to response DTO
func (h *AnalyticsHandler) convertAnalysisToResponse(analysis *entities.ChatAnalysis) dtos.AnalyticsResponse {
	response := dtos.AnalyticsResponse{
		ID:                    analysis.ID,
		ChatID:                analysis.ChatID,
		AnalysisType:          analysis.AnalysisType,
		StartDate:             analysis.StartDate,
		EndDate:               analysis.EndDate,
		TotalMessages:         analysis.TotalMessages,
		MessagesSent:          analysis.MessagesSent,
		MessagesReceived:      analysis.MessagesReceived,
		TotalWords:            analysis.TotalWords,
		TotalCharacters:       analysis.TotalCharacters,
		AvgWordsPerMessage:    analysis.AvgWordsPerMessage,
		MostActiveHour:        analysis.MostActiveHour,
		MostActiveDay:         analysis.MostActiveDay,
		AvgResponseTime:       analysis.AvgResponseTime,
		PositiveMessages:      analysis.PositiveMessages,
		NegativeMessages:      analysis.NegativeMessages,
		NeutralMessages:       analysis.NeutralMessages,
		OverallSentiment:      analysis.OverallSentiment,
		TotalEmojis:           analysis.TotalEmojis,
		UniqueEmojis:          analysis.UniqueEmojis,
		UniqueWords:           analysis.UniqueWords,
		LongestConversation:   analysis.LongestConversation,
		AvgConversationLength: analysis.AvgConversationLength,
		NewContacts:           analysis.NewContacts,
		Insights:              analysis.Insights,
		Summary:               analysis.Summary,
		CreatedAt:             analysis.CreatedAt,
	}

	// Get chat name separately since we removed foreign key
	// This would need to be implemented in analytics service or repository
	response.ChatName = "Chat" // Placeholder - implement proper chat name lookup

	// Parse JSON fields
	var topEmojis []dtos.EmojiStat
	var topWords []dtos.WordStat
	var topContacts []dtos.ContactStat

	json.Unmarshal([]byte(analysis.TopEmojis), &topEmojis)
	json.Unmarshal([]byte(analysis.TopWords), &topWords)
	json.Unmarshal([]byte(analysis.TopContacts), &topContacts)

	response.TopEmojis = topEmojis
	response.TopWords = topWords
	response.TopContacts = topContacts

	return response
}
