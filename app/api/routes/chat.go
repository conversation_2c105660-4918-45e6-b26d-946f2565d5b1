package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-base-project-structure/pkg/domains/chat"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/middleware"
	"github.com/google/uuid"
)

type ChatHandler struct {
	chatService chat.Service
}

func NewChatHandler(chatService chat.Service) *Chat<PERSON>andler {
	return &ChatHandler{
		chatService: chatService,
	}
}

func (h *ChatHandler) RegisterRoutes(router *gin.RouterGroup) {
	chatRoutes := router.Group("/chats")
	chatRoutes.Use(middleware.JWTAuth()) // Require JWT authentication for all chat routes
	{
		chatRoutes.POST("/upload", h.UploadChat)
		chatRoutes.POST("/bulk-upload", h.BulkUploadChats)
		chatRoutes.GET("", h.GetUserChats)
		chatRoutes.GET("/:chatId", h.GetChatDetail)
		chatRoutes.DELETE("/:chatId", h.DeleteChat)
		chatRoutes.GET("/:chatId/status", h.GetProcessingStatus)
		chatRoutes.GET("/:chatId/messages", h.GetChatMessages)
	}
}

// @Summary Upload chat file
// @Description Upload and process a chat export file
// @Tags chats
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Chat export file"
// @Param platform formData string true "Chat platform" Enums(whatsapp,telegram,messenger,imessage,discord,other)
// @Param chat_name formData string false "Custom chat name"
// @Success 200 {object} dtos.ChatUploadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/chats/upload [post]
func (h *ChatHandler) UploadChat(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req dtos.ChatUploadRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.chatService.UploadChat(userID.(uuid.UUID), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Bulk upload chat files
// @Description Upload and process multiple chat export files
// @Tags chats
// @Accept multipart/form-data
// @Produce json
// @Param files formData file true "Chat export files"
// @Param platform formData string true "Chat platform"
// @Success 200 {object} dtos.BulkUploadResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/chats/bulk-upload [post]
func (h *ChatHandler) BulkUploadChats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req dtos.BulkUploadRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Process each file
	response := &dtos.BulkUploadResponse{
		TotalFiles: len(req.Files),
		Results:    []dtos.ChatUploadResponse{},
		Errors:     []string{},
	}

	for _, file := range req.Files {
		uploadReq := &dtos.ChatUploadRequest{
			File:     file,
			Platform: req.Platform,
		}

		result, err := h.chatService.UploadChat(userID.(uuid.UUID), uploadReq)
		if err != nil {
			response.FailedFiles++
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.ProcessedFiles++
			response.Results = append(response.Results, *result)
		}
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Get user chats
// @Description Get all chats for the authenticated user
// @Tags chats
// @Produce json
// @Success 200 {object} dtos.ChatListResponse
// @Failure 500 {object} map[string]interface{}
// @Router /api/chats [get]
func (h *ChatHandler) GetUserChats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	response, err := h.chatService.GetUserChats(userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Get chat detail
// @Description Get detailed information about a specific chat
// @Tags chats
// @Produce json
// @Param chatId path string true "Chat ID"
// @Success 200 {object} dtos.ChatDetailResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/chats/{chatId} [get]
func (h *ChatHandler) GetChatDetail(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	chatIDStr := c.Param("chatId")
	chatID, err := uuid.Parse(chatIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
		return
	}

	response, err := h.chatService.GetChatDetail(userID.(uuid.UUID), chatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// @Summary Delete chat
// @Description Delete a chat and all its messages
// @Tags chats
// @Produce json
// @Param chatId path string true "Chat ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/chats/{chatId} [delete]
func (h *ChatHandler) DeleteChat(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	chatIDStr := c.Param("chatId")
	chatID, err := uuid.Parse(chatIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
		return
	}

	err = h.chatService.DeleteChat(userID.(uuid.UUID), chatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Chat deleted successfully"})
}

// @Summary Get processing status
// @Description Get the processing status of a chat
// @Tags chats
// @Produce json
// @Param chatId path string true "Chat ID"
// @Success 200 {object} dtos.ProcessingStatus
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/chats/{chatId}/status [get]
func (h *ChatHandler) GetProcessingStatus(c *gin.Context) {
	chatIDStr := c.Param("chatId")
	chatID, err := uuid.Parse(chatIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
		return
	}

	status, err := h.chatService.GetProcessingStatus(chatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, status)
}

// @Summary Get chat messages
// @Description Get messages from a specific chat with pagination
// @Tags chats
// @Produce json
// @Param chatId path string true "Chat ID"
// @Param limit query int false "Number of messages to return" default(50)
// @Param offset query int false "Number of messages to skip" default(0)
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/chats/{chatId}/messages [get]
func (h *ChatHandler) GetChatMessages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	chatIDStr := c.Param("chatId")
	chatID, err := uuid.Parse(chatIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chat ID"})
		return
	}

	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 1000 {
		limit = 50
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Verify user has access to this chat
	_, err = h.chatService.GetChatDetail(userID.(uuid.UUID), chatID)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// This would require adding a method to chat service to get messages with pagination
	// For now, return a placeholder response
	c.JSON(http.StatusOK, gin.H{
		"chat_id":  chatID,
		"limit":    limit,
		"offset":   offset,
		"messages": []interface{}{},
		"total":    0,
	})
}
