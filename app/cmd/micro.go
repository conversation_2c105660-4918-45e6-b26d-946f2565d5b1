package cmd

import (
	"fmt"
	"log"

	"github.com/go-base-project-structure/pkg/config"
	"github.com/go-base-project-structure/pkg/cron"
	"github.com/go-base-project-structure/pkg/database"
	"github.com/go-base-project-structure/pkg/dummy"
	"github.com/go-base-project-structure/pkg/server"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	//cache.InitRedis(config.Redis)

	// Seed dummy data if in development mode

	fmt.Println("seed data: ", shouldSeedData())

	if shouldSeedData() {
		log.Println("🌱 Seeding dummy data...")
		seeder := dummy.NewSeeder(database.DBClient())
		if err := seeder.SeedAll(); err != nil {
			log.Printf("❌ Failed to seed dummy data: %v", err)
		}
	}

	cron.MyCron()
	server.LaunchHttpServer(config.App, config.Allows)
}

// shouldSeedData determines if dummy data should be seeded
func shouldSeedData() bool {
	// Seed data if:
	// 1. SEED_DATA environment variable is set to "true"
	// 2. APP_ENV is "development" and SKIP_SEED is not "true"

	/*if os.Getenv("SEED_DATA") == "true" {
		return true
	}

	if os.Getenv("SKIP_SEED") == "true" {
		return false
	}

	appEnv := os.Getenv("APP_ENV")
	return appEnv == "development" || appEnv == ""*/
	return true
}
