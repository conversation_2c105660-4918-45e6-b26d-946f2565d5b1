{"name": "College Friends", "type": "private_group", "id": 123456789, "messages": [{"id": 1, "type": "message", "date": "2024-01-15T10:30:00", "from": "<PERSON>", "from_id": "user123", "text": "Hey everyone! How's the new semester going? 📚"}, {"id": 2, "type": "message", "date": "2024-01-15T10:32:15", "from": "<PERSON>", "from_id": "user456", "text": "Pretty good! Though I'm already drowning in assignments 😅"}, {"id": 3, "type": "message", "date": "2024-01-15T10:35:30", "from": "<PERSON>", "from_id": "user789", "text": "Same here! Anyone want to form a study group?"}, {"id": 4, "type": "message", "date": "2024-01-15T10:37:45", "from": "Sophia", "from_id": "user101", "text": "I'm in! We could meet at the library"}, {"id": 5, "type": "message", "date": "2024-01-15T10:40:22", "from": "<PERSON>", "from_id": "user123", "text": "Great idea! What subjects should we focus on?"}, {"id": 6, "type": "message", "date": "2024-01-15T10:42:15", "from": "<PERSON>", "from_id": "user456", "text": "I need help with calculus and physics 🤓"}, {"id": 7, "type": "message", "date": "2024-01-15T10:45:30", "from": "<PERSON>", "from_id": "user789", "text": "Perfect! I'm good at physics, could use help with literature"}, {"id": 8, "type": "message", "date": "2024-01-15T10:48:45", "from": "Sophia", "from_id": "user101", "text": "I can help with literature! And I need calculus help too"}, {"id": 9, "type": "message", "date": "2024-01-15T11:15:22", "from": "<PERSON>", "from_id": "user123", "text": "This is working out perfectly! When should we meet?"}, {"id": 10, "type": "message", "date": "2024-01-15T11:18:30", "from": "<PERSON>", "from_id": "user456", "text": "How about tomorrow after classes? Around 4 PM?"}, {"id": 11, "type": "message", "date": "2024-01-15T11:20:45", "from": "<PERSON>", "from_id": "user789", "text": "Works for me! I'll bring my physics notes 📝"}, {"id": 12, "type": "message", "date": "2024-01-15T11:22:15", "from": "Sophia", "from_id": "user101", "text": "Same here! I'll bring literature materials"}, {"id": 13, "type": "message", "date": "2024-01-15T14:30:22", "from": "<PERSON>", "from_id": "user123", "text": "Just finished my morning classes. Anyone free for lunch? 🍕"}, {"id": 14, "type": "message", "date": "2024-01-15T14:32:45", "from": "<PERSON>", "from_id": "user456", "text": "I wish! Still have two more classes 😭"}, {"id": 15, "type": "message", "date": "2024-01-15T14:35:30", "from": "<PERSON>", "from_id": "user789", "text": "I'm free! Where do you want to go?"}, {"id": 16, "type": "message", "date": "2024-01-15T14:37:15", "from": "<PERSON>", "from_id": "user123", "text": "How about that new burger place near campus? 🍔"}, {"id": 17, "type": "message", "date": "2024-01-15T14:40:22", "from": "<PERSON>", "from_id": "user789", "text": "Perfect! I've been wanting to try it. See you there in 15?"}, {"id": 18, "type": "message", "date": "2024-01-15T14:42:30", "from": "<PERSON>", "from_id": "user123", "text": "Sounds good! See you soon 👍"}, {"id": 19, "type": "message", "date": "2024-01-15T16:45:15", "from": "<PERSON>", "from_id": "user456", "text": "Finally done with classes! How was lunch?"}, {"id": 20, "type": "message", "date": "2024-01-15T16:47:30", "from": "<PERSON>", "from_id": "user789", "text": "Amazing! The burgers were incredible 🤤"}, {"id": 21, "type": "message", "date": "2024-01-15T16:50:45", "from": "<PERSON>", "from_id": "user123", "text": "Definitely going back! <PERSON> and <PERSON> should join us next time"}, {"id": 22, "type": "message", "date": "2024-01-15T16:52:22", "from": "Sophia", "from_id": "user101", "text": "Count me in! I love trying new places 😋"}, {"id": 23, "type": "message", "date": "2024-01-15T17:15:30", "from": "<PERSON>", "from_id": "user456", "text": "So excited for our study session tomorrow! 📖"}, {"id": 24, "type": "message", "date": "2024-01-15T17:18:45", "from": "Sophia", "from_id": "user101", "text": "Me too! It's going to be so helpful"}, {"id": 25, "type": "message", "date": "2024-01-15T17:20:15", "from": "<PERSON>", "from_id": "user789", "text": "Best study group ever! We're going to ace these exams 💪"}, {"id": 26, "type": "message", "date": "2024-01-15T17:22:30", "from": "<PERSON>", "from_id": "user123", "text": "Absolutely! Together we're unstoppable 🚀"}, {"id": 27, "type": "message", "date": "2024-01-15T19:30:45", "from": "<PERSON>", "from_id": "user456", "text": "Good night everyone! See you tomorrow 🌙"}, {"id": 28, "type": "message", "date": "2024-01-15T19:32:15", "from": "Sophia", "from_id": "user101", "text": "Good night! Sweet dreams 😴"}, {"id": 29, "type": "message", "date": "2024-01-15T19:35:30", "from": "<PERSON>", "from_id": "user789", "text": "Night everyone! Tomorrow's going to be great 🌟"}, {"id": 30, "type": "message", "date": "2024-01-15T19:37:45", "from": "<PERSON>", "from_id": "user123", "text": "Good night friends! Love you all ❤️"}]}