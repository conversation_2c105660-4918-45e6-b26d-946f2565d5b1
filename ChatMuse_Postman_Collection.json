{"info": {"_postman_id": "chatmuse-api-collection", "name": "ChatMuse API Collection", "description": "Complete API collection for ChatMuse - Your Conversational Mirror", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "auth_token", "value": "", "type": "string", "description": "JWT token obtained from login endpoint"}, {"key": "chat_id", "value": "", "type": "string"}, {"key": "contact_id", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "item": [{"name": "🏥 Health & Version", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/version", "host": ["{{base_url}}"], "path": ["version"]}}, "response": []}]}, {"name": "🔐 Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"name\": \"New User\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["// Auto-extract token from login response", "if (pm.response.json() && pm.response.json().token) {", "    pm.collectionVariables.set('auth_token', pm.response.json().token);", "    console.log('Auth token updated: ' + pm.response.json().token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Get Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Name\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}, "response": []}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"password123\",\n  \"new_password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/auth/change-password", "host": ["{{base_url}}"], "path": ["auth", "change-password"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}]}, {"name": "💬 Chat Management", "item": [{"name": "Upload Chat File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "sample_data/whatsapp_family.txt"}, {"key": "platform", "value": "whatsapp", "type": "text"}, {"key": "chat_name", "value": "Family Group 2024", "type": "text"}]}, "url": {"raw": "{{base_url}}/chats/upload", "host": ["{{base_url}}"], "path": ["chats", "upload"]}}, "response": []}, {"name": "Bulk Upload Chats", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": ["sample_data/whatsapp_family.txt", "sample_data/telegram_friends.json"]}, {"key": "platform", "value": "whatsapp", "type": "text"}]}, "url": {"raw": "{{base_url}}/chats/bulk-upload", "host": ["{{base_url}}"], "path": ["chats", "bulk-upload"]}}, "response": []}, {"name": "Get User Chats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/chats", "host": ["{{base_url}}"], "path": ["chats"]}}, "response": []}, {"name": "Get Chat Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/chats/{{chat_id}}", "host": ["{{base_url}}"], "path": ["chats", "{{chat_id}}"]}}, "response": []}, {"name": "Delete Chat", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/chats/{{chat_id}}", "host": ["{{base_url}}"], "path": ["chats", "{{chat_id}}"]}}, "response": []}, {"name": "Get Processing Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/chats/{{chat_id}}/status", "host": ["{{base_url}}"], "path": ["chats", "{{chat_id}}", "status"]}}, "response": []}, {"name": "Get Chat Messages", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/chats/{{chat_id}}/messages?limit=50&offset=0", "host": ["{{base_url}}"], "path": ["chats", "{{chat_id}}", "messages"], "query": [{"key": "limit", "value": "50"}, {"key": "offset", "value": "0"}]}}, "response": []}]}, {"name": "📊 Chat Analytics", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/chats/{{chat_id}}/analyze", "host": ["{{base_url}}"], "path": ["analytics", "chats", "{{chat_id}}", "analyze"]}}, "response": []}, {"name": "Get Chat Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/chats/{{chat_id}}", "host": ["{{base_url}}"], "path": ["analytics", "chats", "{{chat_id}}"]}}, "response": []}, {"name": "Get Chat Insights", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/chats/{{chat_id}}/insights", "host": ["{{base_url}}"], "path": ["analytics", "chats", "{{chat_id}}", "insights"]}}, "response": []}, {"name": "Get User Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/user?type=daily", "host": ["{{base_url}}"], "path": ["analytics", "user"], "query": [{"key": "type", "value": "daily", "description": "daily, weekly, monthly, yearly, wrapped"}]}}, "response": []}]}, {"name": "🎁 Wrapped Summaries", "item": [{"name": "Generate Wrapped Summary", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped/2024", "host": ["{{base_url}}"], "path": ["analytics", "wrapped", "2024"]}}, "response": []}, {"name": "Get Wrapped History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped", "host": ["{{base_url}}"], "path": ["analytics", "wrapped"]}}, "response": []}, {"name": "Get Specific Year Wrapped", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped/2024", "host": ["{{base_url}}"], "path": ["analytics", "wrapped", "2024"]}}, "response": []}, {"name": "Generate Monthly Wrapped", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped/2024/12", "host": ["{{base_url}}"], "path": ["analytics", "wrapped", "2024", "12"]}}, "response": []}]}, {"name": "🕸️ Social Network", "item": [{"name": "Analyze Social Network", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/social/analyze", "host": ["{{base_url}}"], "path": ["analytics", "social", "analyze"]}}, "response": []}, {"name": "Get Social Network", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/social", "host": ["{{base_url}}"], "path": ["analytics", "social"]}}, "response": []}, {"name": "Get Strongest Connections", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/social/strongest?limit=10", "host": ["{{base_url}}"], "path": ["analytics", "social", "strongest"], "query": [{"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Weakest Connections", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/social/weakest?limit=10", "host": ["{{base_url}}"], "path": ["analytics", "social", "weakest"], "query": [{"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Relationship Trends", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/social/trends", "host": ["{{base_url}}"], "path": ["analytics", "social", "trends"]}}, "response": []}, {"name": "Get Contact Relationship", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/social/contacts/{{contact_id}}", "host": ["{{base_url}}"], "path": ["analytics", "social", "contacts", "{{contact_id}}"]}}, "response": []}]}, {"name": "😊 Emoji & Word Analysis", "item": [{"name": "Get User Emoji Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/user/emojis", "host": ["{{base_url}}"], "path": ["analytics", "user", "emojis"]}}, "response": []}, {"name": "Get Chat Emoji Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/user/emojis?chat_id={{chat_id}}", "host": ["{{base_url}}"], "path": ["analytics", "user", "emojis"], "query": [{"key": "chat_id", "value": "{{chat_id}}"}]}}, "response": []}, {"name": "Get User Word Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/user/words", "host": ["{{base_url}}"], "path": ["analytics", "user", "words"]}}, "response": []}, {"name": "Get Chat Word Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/user/words?chat_id={{chat_id}}", "host": ["{{base_url}}"], "path": ["analytics", "user", "words"], "query": [{"key": "chat_id", "value": "{{chat_id}}"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set auth token if not present", "if (!pm.collectionVariables.get('auth_token')) {", "    pm.collectionVariables.set('auth_token', 'demo_token_123');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-extract chat_id from upload responses", "if (pm.response.json() && pm.response.json().chat_id) {", "    pm.collectionVariables.set('chat_id', pm.response.json().chat_id);", "    console.log('Chat ID set to: ' + pm.response.json().chat_id);", "}", "", "// Auto-extract contact_id from social network responses", "if (pm.response.json() && pm.response.json().networks && pm.response.json().networks.length > 0) {", "    pm.collectionVariables.set('contact_id', pm.response.json().networks[0].contact_id);", "    console.log('Contact ID set to: ' + pm.response.json().networks[0].contact_id);", "}", "", "// Log response status", "console.log('Response Status: ' + pm.response.status);", "console.log('Response Time: ' + pm.response.responseTime + 'ms');"]}}]}