app:
  name: chatmuse
  port: 8000
  host: 0.0.0.0
  base_url: localhost:8000
  jwt_issuer: "chatmuse"
  jwt_secret: "chatmuse_super_secret_key_2024"
  client_id: chatmuse_client
  onesignal_api_key: your_onesignal_key
  force_update_key: chatmuse_update_key_2024
  jwt_expire: 24
database:
  host: chatmuse-db
  port: 5432
  user: chatmuse_user
  pass: chatmuse_password
  name: chatmuse_db

allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000