version: "3.8"
services:
  saye-db:
    image: "postgres:15"
    container_name: chatmuse-db
    volumes:
      - chatmuse_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - TZ="Europe/Istanbul"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  chatmuse:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: chatmuse
    environment:
      - TZ="Europe/Istanbul"
      - APP_ENV=docker
    container_name: chatmuse-app
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 8000:8000
    depends_on:
      saye-db:
        condition: service_healthy
  
volumes:
  chatmuse_data:

networks:
  main:
    name: main_network
    driver: bridge