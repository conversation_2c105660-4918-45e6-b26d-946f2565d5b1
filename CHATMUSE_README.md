# ChatMuse: Your Conversational Mirror 🎯

ChatMuse backend MVP'si tama<PERSON><PERSON>! Bu uygulama kullanıcıların chat geçmişlerini analiz ederek AI destekli kişisel içgör<PERSON><PERSON>, duygu analizleri, konu<PERSON>ma tarzı istatistikleri ve yıllık "Wrapped" özetleri sunar.

## 🚀 Tamamlanan Özellikler

### 1. Chat Upload & Processing
- **Multi-platform support**: WhatsApp, Telegram, Discord, Messenger, iMessage
- **File format support**: JSON, text exports
- **Bulk upload**: Birden fazla chat dosyası yükleme
- **Real-time processing**: Chat dosyalarının anlık işlenmesi

### 2. Chat Analytics Engine
- **Message frequency analysis**: Mesaj sıklığı ve zaman dağılımı
- **Word & character count**: Kelime ve karakter sayısı analizi
- **Emoji analysis**: En çok kullanılan emojiler
- **Sentiment analysis**: Duygu durumu analizi (pozitif/negatif/nötr)
- **Response time analysis**: Yan<PERSON>t süreleri
- **Conversation patterns**: Konuşma kalıpları ve oturumlar

### 3. Wrapped Summary Generator
- **Yearly wrapped**: Yıllık chat özetleri
- **Monthly wrapped**: Aylık özetler
- **Top statistics**: En çok konuşulan kişi, kelimeler, emojiler
- **Time patterns**: En aktif gün/saat analizi
- **Growth metrics**: Önceki yılla karşılaştırma
- **Visual data**: Grafik verileri için hazır JSON

### 4. Social Network Analysis
- **Relationship mapping**: İlişki haritası
- **Relationship scores**: İlişki güçlülük skorları
- **Communication patterns**: İletişim kalıpları
- **Trend analysis**: İlişki trendleri (artan/azalan/stabil)
- **Response rates**: Yanıt oranları
- **Engagement metrics**: Etkileşim metrikleri

## 📁 Proje Yapısı

```
pkg/
├── entities/           # Database entities
│   ├── chat.go        # Chat, Contact, Message entities
│   ├── analysis.go    # Analysis related entities
│   └── user.go        # User entity
├── domains/           # Business logic
│   ├── chat/          # Chat upload & processing
│   ├── analytics/     # Analytics engine
│   ├── social/        # Social network analysis
│   └── wrapped/       # Wrapped summaries
├── dtos/              # Data transfer objects
│   ├── chat.go        # Chat related DTOs
│   └── analytics.go   # Analytics DTOs
└── api/routes/        # API endpoints
    ├── chat.go        # Chat endpoints
    └── analytics.go   # Analytics endpoints
```

## 🔗 API Endpoints

### Chat Management
```
POST   /api/v1/chats/upload           # Chat dosyası yükleme
POST   /api/v1/chats/bulk-upload      # Toplu chat yükleme
GET    /api/v1/chats                  # Kullanıcı chatlerini listele
GET    /api/v1/chats/{id}             # Chat detayları
DELETE /api/v1/chats/{id}             # Chat silme
GET    /api/v1/chats/{id}/status      # İşlem durumu
```

### Analytics
```
POST   /api/v1/analytics/chats/{id}/analyze    # Chat analizi
GET    /api/v1/analytics/chats/{id}            # Chat analiz sonuçları
GET    /api/v1/analytics/chats/{id}/insights   # Chat içgörüleri
GET    /api/v1/analytics/user                  # Kullanıcı analizleri
GET    /api/v1/analytics/user/emojis           # Emoji analizi
GET    /api/v1/analytics/user/words            # Kelime analizi
```

### Wrapped Summaries
```
POST   /api/v1/analytics/wrapped/{year}        # Yıllık wrapped oluştur
GET    /api/v1/analytics/wrapped               # Wrapped geçmişi
GET    /api/v1/analytics/wrapped/{year}        # Belirli yıl wrapped
POST   /api/v1/analytics/wrapped/{year}/{month} # Aylık wrapped
```

### Social Network
```
POST   /api/v1/analytics/social/analyze        # Sosyal ağ analizi
GET    /api/v1/analytics/social                # Sosyal ağ verileri
GET    /api/v1/analytics/social/strongest      # En güçlü bağlantılar
GET    /api/v1/analytics/social/weakest        # En zayıf bağlantılar
GET    /api/v1/analytics/social/trends         # İlişki trendleri
GET    /api/v1/analytics/social/contacts/{id}  # Kişi ilişki detayı
```

## 🛠 Teknoloji Stack

- **Backend**: Go (Gin framework)
- **Database**: PostgreSQL (GORM ORM)
- **Cache**: Redis
- **Documentation**: Swagger
- **Architecture**: Clean Architecture (Domain-driven design)

## 📊 Database Schema

### Core Entities
- **User**: Kullanıcı bilgileri
- **Chat**: Chat bilgileri (platform, isim, grup/bireysel)
- **Contact**: Kişi bilgileri ve istatistikleri
- **Message**: Mesaj verileri ve metadata

### Analytics Entities
- **ChatAnalysis**: Chat analiz sonuçları
- **EmojiUsage**: Emoji kullanım istatistikleri
- **WordUsage**: Kelime kullanım istatistikleri
- **ConversationSession**: Konuşma oturumları
- **SocialNetwork**: Sosyal ağ analizi

## 🚀 Çalıştırma

### Hızlı Başlangıç (Önerilen)
```bash
# Tüm servisleri Docker ile başlat (database + app)
make hot

# Veya ayrı ayrı
make docker-up  # Database + Redis
make dev        # Uygulama
```

### Manuel Kurulum
1. **Dependencies yükle**:
```bash
make deps
# veya
go mod tidy
```

2. **Environment variables** (`.env` dosyası hazır):
```bash
# Gerekirse .env dosyasını düzenle
POSTGRES_USER=chatmuse_user
POSTGRES_PASSWORD=chatmuse_password
POSTGRES_DB=chatmuse_db
SEED_DATA=true  # Dummy data için
```

3. **Servisleri başlat**:
```bash
# Docker ile (önerilen)
make docker-up

# Uygulamayı çalıştır
make dev
```

4. **Test et**:
```bash
# Tam akış testi
./test_chatmuse.sh

# Health check
make health
```

### 🌐 Erişim Linkleri
- **API**: http://localhost:8000
- **Swagger**: http://localhost:8000/docs (chatmuse/chatmuse123)
- **PgAdmin**: http://localhost:5050 (<EMAIL>/chatmuse123)
- **Database**: localhost:5433 (chatmuse_user/chatmuse_password)

## 📝 Kullanım Örnekleri

### 🎯 Tam Akış Testi
```bash
# Otomatik test script'i çalıştır
./test_chatmuse.sh
```

### 📤 Chat Yükleme
```bash
# WhatsApp chat yükle
curl -X POST http://localhost:8000/api/v1/chats/upload \
  -F "file=@sample_data/whatsapp_family.txt" \
  -F "platform=whatsapp" \
  -F "chat_name=Family Group 2024"

# Telegram chat yükle
curl -X POST http://localhost:8000/api/v1/chats/upload \
  -F "file=@sample_data/telegram_friends.json" \
  -F "platform=telegram" \
  -F "chat_name=College Friends"
```

### 🔐 Authentication

#### **JWT Authentication (Required)**
```bash
# 1. Login with demo user
TOKEN=$(curl -s -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' | jq -r '.token')

# 2. Use Token for API calls
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/chats
```

#### **Demo Users (Pre-created)**
```bash
<EMAIL>  → password123  # Ana demo kullanıcı
<EMAIL>  → password123  # Test kullanıcısı
<EMAIL> → admin123     # Admin kullanıcı
```

### 📊 Analiz İşlemleri
```bash
# Chat analizi
curl -X POST http://localhost:8000/api/v1/analytics/chats/{CHAT_ID}/analyze \
  -H "Authorization: Bearer $TOKEN"

# 2024 Wrapped oluştur
curl -X POST http://localhost:8000/api/v1/analytics/wrapped/2024 \
  -H "Authorization: Bearer $TOKEN"

# Sosyal ağ analizi
curl -X POST http://localhost:8000/api/v1/analytics/social/analyze \
  -H "Authorization: Bearer $TOKEN"
```

### 🎁 Dummy Data
Uygulama ilk çalıştırıldığında otomatik olarak dummy data oluşturulur:
- 2 demo kullanıcı
- 16 kişi (her kullanıcı için)
- 12 chat (WhatsApp, Telegram, Discord, Messenger)
- 1000+ mesaj
- Analiz sonuçları ve wrapped özetleri

## 🎯 Gelecek Özellikler

- [ ] Real-time chat import (API integrations)
- [ ] Advanced sentiment analysis (ML models)
- [ ] Voice message analysis
- [ ] Group chat dynamics analysis
- [ ] Export functionality (PDF reports)
- [ ] Mobile app integration
- [ ] Multi-language support

## 🔧 Geliştirme Notları

- Tüm servisler dependency injection ile bağlanmış
- Clean architecture prensiplerine uygun
- Comprehensive error handling
- Swagger documentation hazır
- Database migrations otomatik
- Redis cache entegrasyonu mevcut
- Prometheus metrics desteği

Backend MVP'si tamamen hazır! Frontend geliştirme için API'lar kullanıma hazır durumda.
