{"info": {"_postman_id": "chatmuse-test-scenarios", "name": "ChatMuse Test Scenarios", "description": "Pre-configured test scenarios for ChatMuse API testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "string"}], "item": [{"name": "🚀 Quick Start Flow", "item": [{"name": "1. Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has version', function () {", "    pm.expect(pm.response.json()).to.have.property('version');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/version", "host": ["{{base_url}}"], "path": ["version"]}}}, {"name": "2. <PERSON> Existing Chats", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has chats array', function () {", "    pm.expect(pm.response.json()).to.have.property('chats');", "});", "", "// Store first chat ID if available", "const response = pm.response.json();", "if (response.chats && response.chats.length > 0) {", "    pm.collectionVariables.set('existing_chat_id', response.chats[0].id);", "    console.log('Found existing chat: ' + response.chats[0].name);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/chats", "host": ["{{base_url}}"], "path": ["chats"]}}}, {"name": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "event": [{"listen": "prerequest", "script": {"exec": ["// Skip if no existing chat", "if (!pm.collectionVariables.get('existing_chat_id')) {", "    console.log('No existing chat found, skipping analysis');", "    pm.execution.skipRequest();", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('Analysis completed', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has analysis data', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('total_messages');", "    pm.expect(response).to.have.property('overall_sentiment');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/chats/{{existing_chat_id}}/analyze", "host": ["{{base_url}}"], "path": ["analytics", "chats", "{{existing_chat_id}}", "analyze"]}}}, {"name": "4. Generate 2024 Wrapped", "event": [{"listen": "test", "script": {"exec": ["pm.test('Wrapped generated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has wrapped data', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('year');", "    pm.expect(response).to.have.property('total_messages');", "    pm.expect(response).to.have.property('highlights');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped/2024", "host": ["{{base_url}}"], "path": ["analytics", "wrapped", "2024"]}}}, {"name": "5. Social Network Analysis", "event": [{"listen": "test", "script": {"exec": ["pm.test('Social analysis completed', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has network data', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('networks');", "    pm.expect(response).to.have.property('total_contacts');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/social/analyze", "host": ["{{base_url}}"], "path": ["analytics", "social", "analyze"]}}}]}, {"name": "📤 File Upload Tests", "item": [{"name": "Upload WhatsApp Chat", "event": [{"listen": "test", "script": {"exec": ["pm.test('Upload successful', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has chat_id', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('chat_id');", "    pm.collectionVariables.set('uploaded_chat_id', response.chat_id);", "});", "", "pm.test('Processing stats available', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('stats');", "    pm.expect(response.stats).to.have.property('total_messages');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "sample_data/whatsapp_family.txt", "description": "WhatsApp chat export file"}, {"key": "platform", "value": "whatsapp", "type": "text"}, {"key": "chat_name", "value": "Test Family Group", "type": "text"}]}, "url": {"raw": "{{base_url}}/chats/upload", "host": ["{{base_url}}"], "path": ["chats", "upload"]}}}, {"name": "Analyze Uploaded Chat", "event": [{"listen": "prerequest", "script": {"exec": ["// Wait a bit for processing", "setTimeout(function(){}, 1000);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('Analysis successful', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Analysis contains insights', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('insights');", "    pm.expect(response).to.have.property('top_emojis');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/chats/{{uploaded_chat_id}}/analyze", "host": ["{{base_url}}"], "path": ["analytics", "chats", "{{uploaded_chat_id}}", "analyze"]}}}]}, {"name": "📊 Analytics Deep Dive", "item": [{"name": "Get Chat Insights", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/chats/{{existing_chat_id}}/insights", "host": ["{{base_url}}"], "path": ["analytics", "chats", "{{existing_chat_id}}", "insights"]}}}, {"name": "Emoji Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/user/emojis", "host": ["{{base_url}}"], "path": ["analytics", "user", "emojis"]}}}, {"name": "Word Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/user/words", "host": ["{{base_url}}"], "path": ["analytics", "user", "words"]}}}, {"name": "Strongest Connections", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/social/strongest?limit=5", "host": ["{{base_url}}"], "path": ["analytics", "social", "strongest"], "query": [{"key": "limit", "value": "5"}]}}}, {"name": "Relationship Trends", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/social/trends", "host": ["{{base_url}}"], "path": ["analytics", "social", "trends"]}}}]}, {"name": "🎁 Wrapped Features", "item": [{"name": "Generate 2023 Wrapped", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped/2023", "host": ["{{base_url}}"], "path": ["analytics", "wrapped", "2023"]}}}, {"name": "Generate Monthly Wrapped", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped/2024/1", "host": ["{{base_url}}"], "path": ["analytics", "wrapped", "2024", "1"]}}}, {"name": "Get All Wrapped History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/wrapped", "host": ["{{base_url}}"], "path": ["analytics", "wrapped"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default auth token", "pm.collectionVariables.set('auth_token', 'demo_token_123');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test for response time", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Log response details", "console.log('Request: ' + pm.request.method + ' ' + pm.request.url);", "console.log('Status: ' + pm.response.status + ' (' + pm.response.responseTime + 'ms)');"]}}]}