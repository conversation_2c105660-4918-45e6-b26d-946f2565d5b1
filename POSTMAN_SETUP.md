# 📮 ChatMuse Postman Setup Guide

Bu rehber ChatMuse API'sini Postman'de test etmek için gerekli collection'ları ve environment'ları nasıl import edeceğinizi gösterir.

## 📁 Dosyalar

Proje içinde şu Postman dosyaları bulunur:

1. **`ChatMuse_Postman_Collection.json`** - Ana API collection'ı
2. **`ChatMuse_Test_Scenarios.json`** - Test senaryoları collection'ı  
3. **`ChatMuse_Postman_Environment.json`** - Environment variables

## 🚀 Kurulum Adımları

### 1. Postman'i Aç
- Postman uygulamasını başlat
- Workspace'inizi seçin

### 2. Collection'ları Import Et

#### Ana Collection Import:
1. **Import** butonuna tıkla
2. **Upload Files** seç
3. `ChatMuse_Postman_Collection.json` dosyasını seç
4. **Import** butonuna tıkla

#### Test Scenarios Import:
1. **Import** butonuna tıkla
2. **Upload Files** seç  
3. `ChatMuse_Test_Scenarios.json` dosyasını seç
4. **Import** butonuna tıkla

### 3. Environment Import Et
1. **Environments** sekmesine git
2. **Import** butonuna tıkla
3. `ChatMuse_Postman_Environment.json` dosyasını seç
4. **Import** butonuna tıkla

### 4. Environment'ı Aktif Et
1. Sağ üst köşeden environment dropdown'unu aç
2. **"ChatMuse Development"** environment'ını seç

## 📊 Collection Yapısı

### 🏥 Ana Collection (ChatMuse API Collection)

```
📁 ChatMuse API Collection
├── 🏥 Health & Version
│   └── Health Check
├── 💬 Chat Management  
│   ├── Upload Chat File
│   ├── Bulk Upload Chats
│   ├── Get User Chats
│   ├── Get Chat Detail
│   ├── Delete Chat
│   ├── Get Processing Status
│   └── Get Chat Messages
├── 📊 Chat Analytics
│   ├── Analyze Chat
│   ├── Get Chat Analytics
│   ├── Get Chat Insights
│   └── Get User Analytics
├── 🎁 Wrapped Summaries
│   ├── Generate Wrapped Summary
│   ├── Get Wrapped History
│   ├── Get Specific Year Wrapped
│   └── Generate Monthly Wrapped
├── 🕸️ Social Network
│   ├── Analyze Social Network
│   ├── Get Social Network
│   ├── Get Strongest Connections
│   ├── Get Weakest Connections
│   ├── Get Relationship Trends
│   └── Get Contact Relationship
└── 😊 Emoji & Word Analysis
    ├── Get User Emoji Analysis
    ├── Get Chat Emoji Analysis
    ├── Get User Word Analysis
    └── Get Chat Word Analysis
```

### 🧪 Test Scenarios Collection

```
📁 ChatMuse Test Scenarios
├── 🚀 Quick Start Flow
│   ├── 1. Health Check
│   ├── 2. Get Existing Chats
│   ├── 3. Analyze Existing Chat
│   ├── 4. Generate 2024 Wrapped
│   └── 5. Social Network Analysis
├── 📤 File Upload Tests
│   ├── Upload WhatsApp Chat
│   └── Analyze Uploaded Chat
├── 📊 Analytics Deep Dive
│   ├── Get Chat Insights
│   ├── Emoji Analysis
│   ├── Word Analysis
│   ├── Strongest Connections
│   └── Relationship Trends
└── 🎁 Wrapped Features
    ├── Generate 2023 Wrapped
    ├── Generate Monthly Wrapped
    └── Get All Wrapped History
```

## ⚙️ Environment Variables

| Variable | Değer | Açıklama |
|----------|-------|----------|
| `base_url` | `http://localhost:8000/api/v1` | API base URL |
| `auth_token` | `demo_token_123` | Authentication token |
| `chat_id` | `""` | Otomatik set edilir |
| `contact_id` | `""` | Otomatik set edilir |
| `user_id` | `demo_user_id` | Demo user ID |
| `swagger_url` | `http://localhost:8000/docs` | Swagger documentation |
| `pgadmin_url` | `http://localhost:5050` | PgAdmin URL |

## 🎯 Kullanım Rehberi

### 1. Hızlı Test (Önerilen)
1. **"ChatMuse Test Scenarios"** collection'ını aç
2. **"🚀 Quick Start Flow"** folder'ını aç
3. **"Run collection"** butonuna tıkla
4. Tüm testlerin çalışmasını izle

### 2. Manuel Test
1. **"ChatMuse API Collection"** collection'ını aç
2. İstediğin endpoint'i seç
3. Gerekirse parametreleri düzenle
4. **Send** butonuna tıkla

### 3. File Upload Test
1. `sample_data/` klasöründeki dosyaları hazırla
2. **"Upload Chat File"** request'ini aç
3. **Body > form-data** sekmesinde file'ı seç
4. **Send** butonuna tıkla

## 🔧 Otomatik Özellikler

### Auto-Variables
- **chat_id**: Upload response'undan otomatik extract edilir
- **contact_id**: Social network response'undan otomatik extract edilir
- **auth_token**: Otomatik olarak header'a eklenir

### Auto-Tests
- Response time kontrolü (< 5000ms)
- Status code kontrolü
- Response structure validation
- Console logging

## 📝 Test Örnekleri

### Basit Health Check
```javascript
pm.test('Status code is 200', function () {
    pm.response.to.have.status(200);
});

pm.test('Response has version', function () {
    pm.expect(pm.response.json()).to.have.property('version');
});
```

### Chat Upload Validation
```javascript
pm.test('Upload successful', function () {
    pm.response.to.have.status(200);
});

pm.test('Response has chat_id', function () {
    const response = pm.response.json();
    pm.expect(response).to.have.property('chat_id');
    pm.collectionVariables.set('chat_id', response.chat_id);
});
```

## 🚨 Troubleshooting

### Connection Error
- ChatMuse server'ının çalıştığından emin ol: `make dev`
- Base URL'in doğru olduğunu kontrol et: `http://localhost:8000/api/v1`

### File Upload Error
- Sample dosyalarının mevcut olduğunu kontrol et
- File path'in doğru olduğunu kontrol et
- Platform parametresinin doğru olduğunu kontrol et

### Authentication Error
- Auth token'ın set edildiğini kontrol et
- Environment'ın aktif olduğunu kontrol et

## 🎉 Başarılı Test Sonuçları

Başarılı test sonrasında şunları göreceksin:

✅ **Health Check**: API çalışıyor  
✅ **Chat Upload**: Dosya yüklendi ve işlendi  
✅ **Analysis**: Chat analiz edildi  
✅ **Wrapped**: Yıllık özet oluşturuldu  
✅ **Social Network**: İlişki analizi tamamlandı  

## 📚 Ek Kaynaklar

- **Swagger Documentation**: http://localhost:8000/docs
- **PgAdmin**: http://localhost:5050  
- **Test Script**: `./test_chatmuse.sh`
- **Sample Data**: `sample_data/` klasörü

## 💡 Pro Tips

1. **Collection Runner** kullanarak tüm testleri otomatik çalıştır
2. **Environment variables** kullanarak farklı ortamlar için test et
3. **Pre-request scripts** ile dynamic data oluştur
4. **Tests** sekmesinde custom validation'lar yaz
5. **Console** output'unu takip et

Postman collection'ları ile ChatMuse API'sini kolayca test edebilirsin! 🚀
