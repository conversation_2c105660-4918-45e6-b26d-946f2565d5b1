# ChatMuse Setup Guide 🚀

Bu guide ChatMuse backend'ini local ve Docker ortamında çalıştırmak için gerekli adımları içerir.

## 📋 <PERSON><PERSON><PERSON><PERSON><PERSON>

- **Go 1.21+**
- **Docker & Docker Compose**
- **PostgreSQL 15+** (local çalıştırma için)
- **Redis 7+** (local çalıştırma için)

## 🔧 Kurulum

### 1. Repository'yi <PERSON>t
```bash
git clone <repository-url>
cd chat-muse
```

### 2. Environment Variables
`.env` dosyası zaten hazır durumda. <PERSON><PERSON><PERSON><PERSON> halinde değiştirebilirsin:

```bash
# Database credentials
POSTGRES_USER=chatmuse_user
POSTGRES_PASSWORD=chatmuse_password
POSTGRES_DB=chatmuse_db

# Redis password
REDIS_PASSWORD=chatmuse_redis_pass

# Swagger credentials
SWAGGER_USER=chatmuse
SWAGGER_PASS=chatmuse123
```

### 3. Dependencies Yükle
```bash
make deps
# veya
go mod tidy
```

## 🐳 Docker ile Çalıştırma (Önerilen)

### Hızlı Başlangıç
```bash
# Tüm servisleri başlat (database, redis, app)
make hot

# Veya ayrı ayrı
make docker-up    # Sadece database ve redis
make dev          # Uygulamayı local çalıştır
```

### Docker Servisleri
- **PostgreSQL**: `localhost:5433`
- **Redis**: `localhost:6380`
- **PgAdmin**: `localhost:5050`
- **ChatMuse API**: `localhost:8000`

### PgAdmin Erişimi
- URL: http://localhost:5050
- Email: <EMAIL>
- Password: chatmuse123

Database bağlantısı:
- Host: saye-db
- Port: 5432
- Username: chatmuse_user
- Password: chatmuse_password
- Database: chatmuse_db

## 💻 Local Çalıştırma

### 1. PostgreSQL Kurulumu
```bash
# macOS
brew install postgresql@15
brew services start postgresql@15

# Ubuntu
sudo apt install postgresql-15 postgresql-contrib
sudo systemctl start postgresql
```

### 2. Redis Kurulumu
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu
sudo apt install redis-server
sudo systemctl start redis
```

### 3. Database Oluştur
```bash
# PostgreSQL'e bağlan
psql postgres

# User ve database oluştur
CREATE USER chatmuse_user WITH PASSWORD 'chatmuse_password';
CREATE DATABASE chatmuse_db OWNER chatmuse_user;
GRANT ALL PRIVILEGES ON DATABASE chatmuse_db TO chatmuse_user;
\q
```

### 4. Uygulamayı Çalıştır
```bash
make dev
# veya
go run main.go
```

## 📚 API Documentation

Uygulama çalıştıktan sonra Swagger documentation'a erişebilirsin:

- **URL**: http://localhost:8000/docs
- **Username**: chatmuse
- **Password**: chatmuse123

## 🧪 Test Etme

### Health Check
```bash
make health
# veya
curl http://localhost:8000/api/v1/version
```

### Chat Upload Test
```bash
curl -X POST http://localhost:8000/api/v1/chats/upload \
  -F "file=@sample_chat.txt" \
  -F "platform=whatsapp" \
  -F "chat_name=Test Chat"
```

## 🛠 Geliştirme Komutları

```bash
# Geliştirme ortamını başlat
make dev

# Docker ile hot reload
make hot

# Testleri çalıştır
make test

# Database'i resetle
make db-reset

# Database shell
make db-shell

# Redis shell
make redis-shell

# Docker logları
make docker-logs

# Tüm servisleri durdur
make docker-down
```

## 📁 Proje Yapısı

```
chat-muse/
├── app/
│   ├── api/routes/          # API endpoints
│   └── cmd/                 # Application entry point
├── pkg/
│   ├── entities/            # Database models
│   ├── domains/             # Business logic
│   ├── dtos/                # Data transfer objects
│   ├── config/              # Configuration
│   ├── database/            # Database connection
│   └── middleware/          # HTTP middleware
├── docs/                    # Swagger documentation
├── .env                     # Environment variables
├── config.yaml              # Local configuration
├── config-hot.yaml          # Docker configuration
├── docker-compose.yml       # Docker services
├── Makefile                 # Development commands
└── README.md               # Project documentation
```

## 🔍 Troubleshooting

### Database Connection Error
```bash
# Docker container'ları kontrol et
docker ps

# Database loglarını kontrol et
docker logs chatmuse-db

# Database'e manuel bağlan
make db-shell
```

### Redis Connection Error
```bash
# Redis container'ını kontrol et
docker logs chatmuse-redis

# Redis'e manuel bağlan
make redis-shell
```

### Port Conflicts
Eğer portlar kullanımda ise `.env` dosyasından değiştirebilirsin:
```bash
# .env dosyasında
POSTGRES_PORT=5434  # 5433 yerine
REDIS_PORT=6381     # 6380 yerine
APP_PORT=8001       # 8000 yerine
```

### Permission Errors
```bash
# Docker permission fix (Linux)
sudo usermod -aG docker $USER
newgrp docker

# File permissions
chmod +x Makefile
```

## 🚀 Production Deployment

### Environment Variables
Production için şu environment variable'ları ayarla:
```bash
APP_ENV=production
JWT_SECRET=your_super_secret_key
POSTGRES_PASSWORD=strong_password
REDIS_PASSWORD=strong_redis_password
```

### Build
```bash
# Production build
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/chatmuse main.go
```

## 📞 Destek

Herhangi bir sorun yaşarsan:
1. Önce bu guide'ı kontrol et
2. Docker loglarını kontrol et: `make docker-logs`
3. Health check yap: `make health`
4. Database bağlantısını test et: `make db-shell`

## 🎯 Sonraki Adımlar

1. **Frontend Development**: API'lar hazır, frontend geliştirmeye başlayabilirsin
2. **Chat File Testing**: Farklı platform chat dosyalarını test et
3. **Analytics Testing**: Chat yükleyip analiz sonuçlarını kontrol et
4. **Wrapped Generation**: Yıllık özetleri test et

Başarılar! 🎉
