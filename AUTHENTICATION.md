# 🔐 ChatMuse Authentication Guide

ChatMuse hem gerçek JWT authentication hem de demo authentication sistemlerini destekler. Bu rehber her iki authentication yönte<PERSON> açıklar.

## 🔑 Real JWT Authentication (Önerilen)

### 📝 User Registration
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "myusername",
    "email": "<EMAIL>",
    "password": "password123",
    "name": "My Name"
  }'
```

**Response:**
```json
{
  "message": "User created successfully",
  "user_id": "123e4567-e89b-12d3-a456-************"
}
```

### 🔐 User Login
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires": "2024-01-16T10:30:00Z",
  "is_succeeded": true,
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "username": "myusername",
    "name": "My Name",
    "email": "<EMAIL>"
  }
}
```

### 👤 User Profile Management
```bash
# Get profile
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8000/api/v1/auth/profile

# Update profile
curl -X PUT -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Name"}' \
  http://localhost:8000/api/v1/auth/profile

# Change password
curl -X POST -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"current_password": "old123", "new_password": "new123"}' \
  http://localhost:8000/api/v1/auth/change-password
```

## 🎯 Demo Users (For Testing)

### 📋 Pre-created Demo Users

| Email | Username | Password | Açıklama |
|-------|----------|----------|----------|
| <EMAIL> | demo_user | password123 | Ana demo kullanıcı |
| <EMAIL> | test_user | password123 | Test kullanıcısı |
| <EMAIL> | admin_user | admin123 | Admin kullanıcı |

> **Not**: Bu user'lar seeder tarafından otomatik oluşturulur. Login endpoint'i ile giriş yapıp JWT token alabilirsiniz.

### 🔑 JWT Token Kullanımı

#### **1. Login ve Token Alma**
```bash
# Login yapıp token al
TOKEN=$(curl -s -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' | jq -r '.token')
```

#### **2. Authorization Header ile Kullanım**
```bash
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8000/api/v1/chats
```

> **Not**: Artık sadece gerçek JWT token'lar kabul edilir. Demo token sistemi kaldırılmıştır.

## 🛡️ Protected vs Public Endpoints

### 🔒 **Protected Endpoints** (JWT Authentication Gerekli)
- `/api/v1/chats/*` - Tüm chat işlemleri
- `/api/v1/analytics/*` - Tüm analiz işlemleri
- `/api/v1/auth/profile` - Profile management
- `/api/v1/auth/change-password` - Password change
- `/api/v1/auth/logout` - Logout

### 🌐 **Public Endpoints** (Authentication Gerekmez)
- `/api/v1/version` - Health check
- `/api/v1/auth/register` - User registration
- `/api/v1/auth/login` - User login
- `/docs` - Swagger documentation

## 📝 Kullanım Örnekleri

### **Postman'de Kullanım**
1. **Collection Variables** sekmesinde:
   ```
   auth_token: demo_token_123
   ```

2. **Authorization** sekmesinde:
   ```
   Type: Bearer Token
   Token: {{auth_token}}
   ```

### **cURL Örnekleri**

#### Chat Listesi
```bash
curl -X GET "http://localhost:8000/api/v1/chats" \
  -H "Authorization: Bearer demo_token_123"
```

#### Chat Upload
```bash
curl -X POST "http://localhost:8000/api/v1/chats/upload" \
  -H "Authorization: Bearer demo_token_123" \
  -F "file=@sample_data/whatsapp_family.txt" \
  -F "platform=whatsapp" \
  -F "chat_name=Test Chat"
```

#### Analytics
```bash
curl -X POST "http://localhost:8000/api/v1/analytics/wrapped/2024" \
  -H "Authorization: Bearer demo_token_123"
```

### **JavaScript/Fetch Örneği**
```javascript
const response = await fetch('http://localhost:8000/api/v1/chats', {
  headers: {
    'Authorization': 'Bearer demo_token_123',
    'Content-Type': 'application/json'
  }
});
```

### **Python/Requests Örneği**
```python
import requests

headers = {
    'Authorization': 'Bearer demo_token_123',
    'Content-Type': 'application/json'
}

response = requests.get('http://localhost:8000/api/v1/chats', headers=headers)
```

## 🔧 Farklı Kullanıcılarla Test

### **Demo User (demo_token_123)**
```bash
# Demo user'ın chatlerini listele
curl -H "Authorization: Bearer demo_token_123" \
  http://localhost:8000/api/v1/chats

# Demo user için wrapped oluştur
curl -X POST -H "Authorization: Bearer demo_token_123" \
  http://localhost:8000/api/v1/analytics/wrapped/2024
```

### **Test User (test_token_456)**
```bash
# Test user'ın chatlerini listele
curl -H "Authorization: Bearer test_token_456" \
  http://localhost:8000/api/v1/chats

# Test user için analiz
curl -X POST -H "Authorization: Bearer test_token_456" \
  http://localhost:8000/api/v1/analytics/social/analyze
```

## ❌ Error Responses

### **Invalid Token**
```json
{
  "error": "Invalid demo token. Use: demo_token_123, test_token_456, or admin_token_789",
  "available_tokens": ["demo_token_123", "test_token_456", "admin_token_789"]
}
```

### **Missing Token (Protected Endpoint)**
```json
{
  "error": "User not authenticated"
}
```

## 🔄 User Context

Authentication başarılı olduğunda şu context bilgileri set edilir:

```go
c.Set("user_id", userID)                    // UUID
c.Set("current_user_id", userID.String())   // String
c.Set("current_user_ip", c.ClientIP())      // IP Address
c.Set("current_device_id", "demo_device")   // Device ID
c.Set("current_timezone", "UTC")            // Timezone
c.Set("current_phone_language", "en")       // Language
```

## 🧪 Test Script Kullanımı

Test script'i otomatik olarak `demo_token_123` kullanır:

```bash
# Default demo user ile test
./test_chatmuse.sh

# Farklı token ile test (script'i düzenle)
# USER_TOKEN="test_token_456" değiştir
./test_chatmuse.sh
```

## 🚀 Production'a Geçiş

Production ortamında gerçek JWT authentication kullanılacak:

1. **JWT Secret**: Environment variable'dan alınacak
2. **User Registration/Login**: Gerçek endpoints eklenecek
3. **Token Expiration**: Token süre sınırı eklenecek
4. **Refresh Tokens**: Token yenileme sistemi
5. **Role-based Access**: Kullanıcı rolleri ve izinler

## 🔍 Debugging

### **Token Kontrolü**
```bash
# Hangi user olarak authenticate olduğunu kontrol et
curl -H "Authorization: Bearer demo_token_123" \
  http://localhost:8000/api/v1/chats | jq
```

### **User ID Kontrolü**
Database'de user ID'leri kontrol et:
```sql
-- PgAdmin'de çalıştır
SELECT id, username, name, email FROM users;
```

### **Log Kontrolü**
Server loglarında authentication bilgilerini kontrol et:
```bash
# Docker logs
make docker-logs

# Local logs
tail -f logs/app.log
```

## 💡 Pro Tips

1. **Postman Environment**: Token'ı environment variable olarak kaydet
2. **Multiple Users**: Farklı collection'lar farklı tokenlar için
3. **Automated Testing**: Test script'lerinde token'ı parametre yap
4. **Development**: Default token kullanarak hızlı test yap
5. **Data Isolation**: Her user kendi verilerini görür

Bu authentication sistemi ile ChatMuse API'sini güvenli şekilde test edebilirsin! 🔐
