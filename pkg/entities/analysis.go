package entities

import (
	"time"

	"github.com/google/uuid"
)

type AnalysisType string

const (
	DailyAnalysis   AnalysisType = "daily"
	WeeklyAnalysis  AnalysisType = "weekly"
	MonthlyAnalysis AnalysisType = "monthly"
	YearlyAnalysis  AnalysisType = "yearly"
	WrappedAnalysis AnalysisType = "wrapped"
)

type ChatAnalysis struct {
	Base
	UserID       uuid.UUID    `json:"user_id" gorm:"not null"`
	ChatID       uuid.UUID    `json:"chat_id" gorm:"not null"`
	AnalysisType AnalysisType `json:"analysis_type" gorm:"not null"`

	// Time period
	StartDate time.Time `json:"start_date" gorm:"not null"`
	EndDate   time.Time `json:"end_date" gorm:"not null"`

	// Basic statistics
	TotalMessages      int     `json:"total_messages"`
	MessagesSent       int     `json:"messages_sent"`
	MessagesReceived   int     `json:"messages_received"`
	TotalWords         int     `json:"total_words"`
	TotalCharacters    int     `json:"total_characters"`
	AvgWordsPerMessage float64 `json:"avg_words_per_message"`

	// Time analysis
	MostActiveHour  int     `json:"most_active_hour"`
	MostActiveDay   string  `json:"most_active_day"`
	AvgResponseTime float64 `json:"avg_response_time"`

	// Sentiment analysis
	PositiveMessages int     `json:"positive_messages"`
	NegativeMessages int     `json:"negative_messages"`
	NeutralMessages  int     `json:"neutral_messages"`
	OverallSentiment float64 `json:"overall_sentiment"`

	// Emoji analysis
	TotalEmojis  int    `json:"total_emojis"`
	UniqueEmojis int    `json:"unique_emojis"`
	TopEmojis    string `json:"top_emojis" gorm:"type:jsonb"`

	// Word analysis
	TopWords    string `json:"top_words" gorm:"type:jsonb"`
	UniqueWords int    `json:"unique_words"`

	// Conversation patterns
	LongestConversation   int     `json:"longest_conversation"`
	AvgConversationLength float64 `json:"avg_conversation_length"`

	// Social metrics
	TopContacts string `json:"top_contacts" gorm:"type:jsonb"`
	NewContacts int    `json:"new_contacts"`

	// Generated insights
	Insights string `json:"insights" gorm:"type:text"`
	Summary  string `json:"summary" gorm:"type:text"`
}

type EmojiUsage struct {
	Base
	UserID   uuid.UUID  `json:"user_id" gorm:"not null"`
	ChatID   *uuid.UUID `json:"chat_id"`
	Emoji    string     `json:"emoji" gorm:"not null"`
	Count    int        `json:"count" gorm:"default:1"`
	LastUsed time.Time  `json:"last_used" gorm:"not null"`
}

type WordUsage struct {
	Base
	UserID   uuid.UUID  `json:"user_id" gorm:"not null"`
	ChatID   *uuid.UUID `json:"chat_id"`
	Word     string     `json:"word" gorm:"not null"`
	Count    int        `json:"count" gorm:"default:1"`
	LastUsed time.Time  `json:"last_used" gorm:"not null"`
}

type ConversationSession struct {
	Base
	ChatID       uuid.UUID `json:"chat_id" gorm:"not null"`
	StartTime    time.Time `json:"start_time" gorm:"not null"`
	EndTime      time.Time `json:"end_time" gorm:"not null"`
	MessageCount int       `json:"message_count" gorm:"default:0"`
	Duration     int64     `json:"duration"` // in seconds
	Participants string    `json:"participants" gorm:"type:jsonb"`
}

type SocialNetwork struct {
	Base
	UserID    uuid.UUID `json:"user_id" gorm:"not null"`
	ContactID uuid.UUID `json:"contact_id" gorm:"not null"`

	// Relationship metrics
	MessageExchanges int     `json:"message_exchanges"`
	ResponseRate     float64 `json:"response_rate"`
	InitiationRate   float64 `json:"initiation_rate"`
	AvgResponseTime  float64 `json:"avg_response_time"`

	// Interaction patterns
	LastInteraction time.Time `json:"last_interaction"`
	InteractionDays int       `json:"interaction_days"`
	StreakDays      int       `json:"streak_days"`

	// Relationship strength
	RelationshipScore float64 `json:"relationship_score"`
	TrendDirection    string  `json:"trend_direction"` // "increasing", "decreasing", "stable"

	// Communication style
	AvgMessageLength float64 `json:"avg_message_length"`
	EmojiUsageRate   float64 `json:"emoji_usage_rate"`
	SentimentMatch   float64 `json:"sentiment_match"`
}
