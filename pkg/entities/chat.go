package entities

import (
	"time"

	"github.com/google/uuid"
)

type ChatPlatform string

const (
	WhatsApp  ChatPlatform = "whatsapp"
	Telegram  ChatPlatform = "telegram"
	Messenger ChatPlatform = "messenger"
	IMessage  ChatPlatform = "imessage"
	Discord   ChatPlatform = "discord"
	Other     ChatPlatform = "other"
)

type Chat struct {
	Base
	UserID   uuid.UUID    `json:"user_id" gorm:"not null"`
	Name     string       `json:"name" gorm:"not null"`
	Platform ChatPlatform `json:"platform" gorm:"not null"`
	IsGroup  bool         `json:"is_group" gorm:"default:false"`

	// Analysis metadata
	TotalMessages  int        `json:"total_messages" gorm:"default:0"`
	FirstMessageAt time.Time  `json:"first_message_at"`
	LastMessageAt  time.Time  `json:"last_message_at"`
	AnalyzedAt     *time.Time `json:"analyzed_at"`
	IsAnalyzed     bool       `json:"is_analyzed" gorm:"default:false"`
}

type Contact struct {
	Base
	UserID      uuid.UUID    `json:"user_id" gorm:"not null"`
	Name        string       `json:"name" gorm:"not null"`
	PhoneNumber string       `json:"phone_number"`
	Username    string       `json:"username"`
	Platform    ChatPlatform `json:"platform" gorm:"not null"`

	// Analysis data
	TotalMessages     int     `json:"total_messages" gorm:"default:0"`
	MessagesSent      int     `json:"messages_sent" gorm:"default:0"`
	MessagesReceived  int     `json:"messages_received" gorm:"default:0"`
	AvgResponseTime   float64 `json:"avg_response_time" gorm:"default:0"`
	RelationshipScore float64 `json:"relationship_score" gorm:"default:0"`
}

type MessageType string

const (
	TextMessage     MessageType = "text"
	ImageMessage    MessageType = "image"
	VideoMessage    MessageType = "video"
	AudioMessage    MessageType = "audio"
	DocumentMessage MessageType = "document"
	StickerMessage  MessageType = "sticker"
	LocationMessage MessageType = "location"
	ContactMessage  MessageType = "contact"
	SystemMessage   MessageType = "system"
)

type Message struct {
	Base
	ChatID      uuid.UUID   `json:"chat_id" gorm:"not null"`
	ContactID   *uuid.UUID  `json:"contact_id"`
	Content     string      `json:"content"`
	MessageType MessageType `json:"message_type" gorm:"not null"`
	Timestamp   time.Time   `json:"timestamp" gorm:"not null"`
	IsFromUser  bool        `json:"is_from_user" gorm:"default:false"`

	// Message metadata
	WordCount  int    `json:"word_count" gorm:"default:0"`
	CharCount  int    `json:"char_count" gorm:"default:0"`
	EmojiCount int    `json:"emoji_count" gorm:"default:0"`
	HasEmoji   bool   `json:"has_emoji" gorm:"default:false"`
	Language   string `json:"language"`

	// Sentiment analysis
	SentimentScore float64 `json:"sentiment_score" gorm:"default:0"`
	SentimentLabel string  `json:"sentiment_label"`

	// Response metadata
	IsReply          bool       `json:"is_reply" gorm:"default:false"`
	ReplyToMessageID *uuid.UUID `json:"reply_to_message_id"`
	ResponseTimeMs   *int64     `json:"response_time_ms"`
}
