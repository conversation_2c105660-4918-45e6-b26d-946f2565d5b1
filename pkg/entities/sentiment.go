package entities

type SentimentType string

const (
	PositiveSentiment SentimentType = "positive"
	NegativeSentiment SentimentType = "negative"
	NeutralSentiment  SentimentType = "neutral"
)

type SentimentWord struct {
	Base
	Word      string        `json:"word" gorm:"not null;index"`
	Language  string        `json:"language" gorm:"not null;default:'tr'"`
	Sentiment SentimentType `json:"sentiment" gorm:"not null"`
	Source    string        `json:"source" gorm:"default:'turkishsentinet'"` // turkishsentinet, swnettr, etc.
}
