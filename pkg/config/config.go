package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

type Config struct {
	App        App        `yaml:"app"`
	Redis      Redis      `yaml:"redis"`
	Database   Database   `yaml:"database"`
	Cloudinary Cloudinary `yaml:"cloudinary"`
	Allows     Allows     `yaml:"allows"`
	Whatsapp   Whatsapp   `yaml:"whatsapp"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	BaseUrl         string `yaml:"base_url"`
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtSecret       string `yaml:"jwt_secret"`
	JwtExpire       int    `yaml:"jwt_expire"`
	ClientID        string `yaml:"client_id"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
	ForceUpdateKey  string `yaml:"force_update_key"`
}

type Redis struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	Pass string `yaml:"pass"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Cloudinary struct {
	Name      string `mapstructure:"name"`
	APIKey    string `mapstructure:"api_key"`
	APISecret string `mapstructure:"api_secret"`
	APIFolder string `mapstructure:"api_folder"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type Whatsapp struct {
	ApiKey string `yaml:"api_key"`
}

func InitConfig() *Config {
	var configs Config
	file_name, _ := filepath.Abs("./config.yaml")
	yaml_file, _ := os.ReadFile(file_name)
	yaml.Unmarshal(yaml_file, &configs)

	// Override with environment variables if they exist
	overrideWithEnvVars(&configs)

	return &configs
}

// overrideWithEnvVars overrides config values with environment variables
func overrideWithEnvVars(config *Config) {
	// App configuration
	if val := os.Getenv("APP_NAME"); val != "" {
		config.App.Name = val
	}
	if val := os.Getenv("APP_PORT"); val != "" {
		config.App.Port = val
	}
	if val := os.Getenv("APP_HOST"); val != "" {
		config.App.Host = val
	}
	if val := os.Getenv("BASE_URL"); val != "" {
		config.App.BaseUrl = val
	}
	if val := os.Getenv("JWT_SECRET"); val != "" {
		config.App.JwtSecret = val
	}
	if val := os.Getenv("JWT_ISSUER"); val != "" {
		config.App.JwtIssuer = val
	}
	if val := os.Getenv("CLIENT_ID"); val != "" {
		config.App.ClientID = val
	}
	if val := os.Getenv("ONESIGNAL_API_KEY"); val != "" {
		config.App.OneSignalAPIKey = val
	}
	if val := os.Getenv("FORCE_UPDATE_KEY"); val != "" {
		config.App.ForceUpdateKey = val
	}

	// Database configuration
	if val := os.Getenv("DB_HOST"); val != "" {
		config.Database.Host = val
	} else if val := os.Getenv("POSTGRES_HOST"); val != "" {
		config.Database.Host = val
	}
	if val := os.Getenv("DB_PORT"); val != "" {
		config.Database.Port = val
	} else if val := os.Getenv("POSTGRES_PORT"); val != "" {
		config.Database.Port = val
	}
	if val := os.Getenv("DB_USER"); val != "" {
		config.Database.User = val
	} else if val := os.Getenv("POSTGRES_USER"); val != "" {
		config.Database.User = val
	}
	if val := os.Getenv("DB_PASSWORD"); val != "" {
		config.Database.Pass = val
	} else if val := os.Getenv("POSTGRES_PASSWORD"); val != "" {
		config.Database.Pass = val
	}
	if val := os.Getenv("DB_NAME"); val != "" {
		config.Database.Name = val
	} else if val := os.Getenv("POSTGRES_DB"); val != "" {
		config.Database.Name = val
	}

	// Redis configuration
	if val := os.Getenv("REDIS_HOST"); val != "" {
		config.Redis.Host = val
	} else if val := os.Getenv("REDIS_DOCKER_HOST"); val != "" {
		config.Redis.Host = val
	}
	if val := os.Getenv("REDIS_PORT"); val != "" {
		config.Redis.Port = val
	} else if val := os.Getenv("REDIS_DOCKER_PORT"); val != "" {
		config.Redis.Port = val
	}
	if val := os.Getenv("REDIS_PASSWORD"); val != "" {
		config.Redis.Pass = val
	}

	// Cloudinary configuration
	if val := os.Getenv("CLOUDINARY_NAME"); val != "" {
		config.Cloudinary.Name = val
	}
	if val := os.Getenv("CLOUDINARY_API_KEY"); val != "" {
		config.Cloudinary.APIKey = val
	}
	if val := os.Getenv("CLOUDINARY_API_SECRET"); val != "" {
		config.Cloudinary.APISecret = val
	}
	if val := os.Getenv("CLOUDINARY_FOLDER"); val != "" {
		config.Cloudinary.APIFolder = val
	}
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}
	filename, _ := filepath.Abs("./config.yaml")
	// Sanitize the destination path using filepath.Clean
	cleanedDst := filepath.Clean(filename)
	yamlFile, _ := os.ReadFile(cleanedDst)
	err := yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error loading config.yaml ", err)
	}
	return configs
}
