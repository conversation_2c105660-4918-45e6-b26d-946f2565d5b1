package dummy

import (
	"github.com/go-base-project-structure/pkg/consts"
	"github.com/go-base-project-structure/pkg/database"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/go-base-project-structure/pkg/utils"
)

func CreateDummy() {
	CreateDummyUser()
}

func CreateDummyUser() {
	db := database.DBClient()

	var user entities.User
	db.Where(consts.UserName+" = ?", "samet").First(&user)
	if user.Username != "" {
		return
	}

	pass := utils.Bcrypt("SametAvci05")
	db.Create(&entities.User{
		Username: "samet",
		Password: pass,
		Name:     "Samet Avci",
	})
}
