package dummy

import (
	"fmt"
	"log"
	"math/rand"
	"strings"
	"time"

	"github.com/go-base-project-structure/pkg/domains/auth"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Seeder struct {
	db *gorm.DB
}

func NewSeeder(db *gorm.DB) *Seeder {
	return &Seeder{db: db}
}

func (s *Seeder) SeedAll() error {
	log.Println("🌱 Starting dummy data seeding...")

	// Check if data already exists
	var userCount int64
	s.db.Model(&entities.User{}).Count(&userCount)
	if userCount > 0 {
		log.Println("📊 Dummy data already exists, skipping seeding")
		return nil
	}

	// Seed in order
	users, err := s.SeedUsers()
	if err != nil {
		return fmt.Errorf("failed to seed users: %w", err)
	}

	contacts, err := s.SeedContacts(users)
	if err != nil {
		return fmt.Errorf("failed to seed contacts: %w", err)
	}

	chats, err := s.SeedChats(users, contacts)
	if err != nil {
		return fmt.Errorf("failed to seed chats: %w", err)
	}

	err = s.SeedMessages(chats, contacts)
	if err != nil {
		return fmt.Errorf("failed to seed messages: %w", err)
	}

	err = s.SeedAnalytics(users, chats)
	if err != nil {
		return fmt.Errorf("failed to seed analytics: %w", err)
	}

	log.Println("✅ Dummy data seeding completed successfully!")
	return nil
}

func (s *Seeder) SeedUsers() ([]entities.User, error) {
	log.Println("👤 Seeding users...")

	// Use auth service to create users properly
	authRepo := auth.NewRepo(s.db)
	authService := auth.NewService(authRepo)

	// Demo users to create
	demoUsers := []dtos.CreateUserReqDto{
		{
			Username: "demo_user",
			Password: "password123", // Will be hashed by service
			Name:     "Demo User",
			Email:    "<EMAIL>",
		},
		{
			Username: "test_user",
			Password: "password123", // Will be hashed by service
			Name:     "Test User",
			Email:    "<EMAIL>",
		},
		{
			Username: "admin_user",
			Password: "admin123", // Will be hashed by service
			Name:     "Admin User",
			Email:    "<EMAIL>",
		},
	}

	var createdUsers []entities.User

	for _, userData := range demoUsers {
		// Check if user already exists
		exists, err := authRepo.UserExists(userData.Email, userData.Username)
		if err != nil {
			log.Printf("❌ Error checking user existence for %s: %v", userData.Username, err)
			continue
		}

		if exists {
			log.Printf("ℹ️ User %s already exists, skipping", userData.Username)
			// Get existing user
			existingUser, err := authRepo.GetUserByEmail(userData.Email)
			if err == nil {
				createdUsers = append(createdUsers, *existingUser)
			}
			continue
		}

		// Create user using auth service (this will hash password and generate UUID)
		_, err = authService.Register(userData)
		if err != nil {
			log.Printf("❌ Error creating user %s: %v", userData.Username, err)
			continue
		}

		// Get created user
		createdUser, err := authRepo.GetUserByEmail(userData.Email)
		if err != nil {
			log.Printf("❌ Error retrieving created user %s: %v", userData.Username, err)
			continue
		}

		createdUsers = append(createdUsers, *createdUser)
		log.Printf("✅ Created user: %s (ID: %s)", createdUser.Username, createdUser.ID)
	}

	log.Printf("✅ Created %d users", len(createdUsers))
	return createdUsers, nil
}

func (s *Seeder) SeedContacts(users []entities.User) ([]entities.Contact, error) {
	log.Println("📞 Seeding contacts...")

	contactNames := []string{
		"Alice Johnson", "Bob Smith", "Charlie Brown", "Diana Prince",
		"Emma Watson", "Frank Miller", "Grace Lee", "Henry Ford",
		"Ivy Chen", "Jack Wilson", "Kate Middleton", "Liam Neeson",
		"Maya Patel", "Noah Davis", "Olivia Taylor", "Paul McCartney",
	}

	platforms := []entities.ChatPlatform{
		entities.WhatsApp, entities.Telegram, entities.Discord, entities.Messenger,
	}

	var contacts []entities.Contact

	for _, user := range users {
		for i, name := range contactNames {
			contact := entities.Contact{
				UserID:            user.ID,
				Name:              name,
				Platform:          platforms[i%len(platforms)],
				TotalMessages:     rand.Intn(500) + 50,
				MessagesSent:      0, // Will be calculated later
				MessagesReceived:  0, // Will be calculated later
				RelationshipScore: rand.Float64() * 100,
			}
			contact.ID = uuid.New()
			contact.CreatedAt = time.Now().AddDate(0, -rand.Intn(6), -rand.Intn(30))
			contact.UpdatedAt = time.Now()
			contacts = append(contacts, contact)
		}
	}

	result := s.db.Create(&contacts)
	if result.Error != nil {
		return nil, result.Error
	}

	log.Printf("✅ Created %d contacts", len(contacts))
	return contacts, nil
}

func (s *Seeder) SeedChats(users []entities.User, contacts []entities.Contact) ([]entities.Chat, error) {
	log.Println("💬 Seeding chats...")

	chatNames := []string{
		"Family Group", "Work Team", "College Friends", "Gaming Squad",
		"Book Club", "Fitness Buddies", "Travel Planning", "Movie Night",
		"Study Group", "Weekend Plans", "Birthday Party", "Project Discussion",
	}

	platforms := []entities.ChatPlatform{
		entities.WhatsApp, entities.Telegram, entities.Discord, entities.Messenger,
	}

	var chats []entities.Chat

	for _, user := range users {
		for i, name := range chatNames {
			isGroup := rand.Float32() > 0.3 // 70% chance of being a group
			totalMessages := rand.Intn(1000) + 100

			firstMessageTime := time.Now().AddDate(0, -rand.Intn(12), -rand.Intn(30))
			lastMessageTime := time.Now().AddDate(0, 0, -rand.Intn(7))

			chat := entities.Chat{
				UserID:         user.ID,
				Name:           name,
				Platform:       platforms[i%len(platforms)],
				IsGroup:        isGroup,
				TotalMessages:  totalMessages,
				FirstMessageAt: firstMessageTime,
				LastMessageAt:  lastMessageTime,
				IsAnalyzed:     rand.Float32() > 0.5, // 50% chance of being analyzed
			}
			chat.ID = uuid.New()
			chat.CreatedAt = firstMessageTime
			chat.UpdatedAt = time.Now()

			if chat.IsAnalyzed {
				analyzedTime := lastMessageTime.Add(time.Hour)
				chat.AnalyzedAt = &analyzedTime
			}

			chats = append(chats, chat)
		}
	}

	result := s.db.Create(&chats)
	if result.Error != nil {
		return nil, result.Error
	}

	// Add participants to chats
	for _, chat := range chats {
		// Get contacts for this user
		var userContacts []entities.Contact
		s.db.Where("user_id = ?", chat.UserID).Limit(5).Find(&userContacts)

		// Add random participants
		participantCount := 1
		if chat.IsGroup {
			participantCount = rand.Intn(4) + 2 // 2-5 participants for groups
		}

		for i := 0; i < participantCount && i < len(userContacts); i++ {
			s.db.Model(&chat).Association("Participants").Append(&userContacts[i])
		}
	}

	log.Printf("✅ Created %d chats", len(chats))
	return chats, nil
}

func (s *Seeder) SeedMessages(chats []entities.Chat, contacts []entities.Contact) error {
	log.Println("📝 Seeding messages...")

	sampleMessages := []string{
		"Hey! How are you doing?", "Great to hear from you!", "What's up?",
		"Thanks for the help yesterday", "Looking forward to our meeting",
		"Have a great day! 😊", "That's awesome news!", "Let me know when you're free",
		"Happy birthday! 🎉", "Good morning!", "See you later!",
		"Thanks for sharing that", "Interesting point!", "I agree with you",
		"Let's catch up soon", "Hope you're doing well", "Take care!",
		"That made me laugh 😂", "Sounds like a plan", "Perfect timing!",
		"I love this idea ❤️", "You're the best!", "Amazing work!",
	}

	emojis := []string{"😊", "😂", "❤️", "👍", "🎉", "🔥", "💯", "😍", "🎄", "🍕"}

	messageTypes := []entities.MessageType{
		entities.TextMessage, entities.ImageMessage, entities.VideoMessage,
		entities.AudioMessage, entities.StickerMessage,
	}

	totalMessages := 0

	for _, chat := range chats {
		// Get participants for this chat
		var participants []entities.Contact
		s.db.Model(&chat).Association("Participants").Find(&participants)

		if len(participants) == 0 {
			continue
		}

		messagesCount := rand.Intn(200) + 50 // 50-250 messages per chat

		for i := 0; i < messagesCount; i++ {
			// Random participant
			participant := participants[rand.Intn(len(participants))]

			// Random message content
			content := sampleMessages[rand.Intn(len(sampleMessages))]

			// Add random emoji sometimes
			if rand.Float32() > 0.7 {
				content += " " + emojis[rand.Intn(len(emojis))]
			}

			// Random timestamp within chat timeframe
			timeDiff := chat.LastMessageAt.Sub(chat.FirstMessageAt)
			randomOffset := time.Duration(rand.Int63n(int64(timeDiff)))
			messageTime := chat.FirstMessageAt.Add(randomOffset)

			message := entities.Message{
				ChatID:         chat.ID,
				ContactID:      &participant.ID,
				Content:        content,
				MessageType:    messageTypes[rand.Intn(len(messageTypes))],
				Timestamp:      messageTime,
				IsFromUser:     rand.Float32() > 0.5, // 50% from user, 50% from contact
				WordCount:      len(strings.Fields(content)),
				CharCount:      len(content),
				HasEmoji:       strings.ContainsAny(content, "😊😂❤️👍🎉🔥💯😍🎄🍕"),
				EmojiCount:     strings.Count(content, "😊") + strings.Count(content, "😂"), // Simplified
				SentimentScore: (rand.Float64() - 0.5) * 2,                                // -1 to 1
			}

			// Set sentiment label
			if message.SentimentScore > 0.1 {
				message.SentimentLabel = "positive"
			} else if message.SentimentScore < -0.1 {
				message.SentimentLabel = "negative"
			} else {
				message.SentimentLabel = "neutral"
			}

			message.ID = uuid.New()
			message.CreatedAt = messageTime
			message.UpdatedAt = messageTime

			s.db.Create(&message)
			totalMessages++
		}
	}

	log.Printf("✅ Created %d messages", totalMessages)
	return nil
}

func (s *Seeder) SeedAnalytics(users []entities.User, chats []entities.Chat) error {
	log.Println("📊 Seeding analytics...")

	for _, user := range users {
		// Create yearly wrapped for 2023
		wrapped := entities.ChatAnalysis{
			UserID:                user.ID,
			AnalysisType:          entities.WrappedAnalysis,
			StartDate:             time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			EndDate:               time.Date(2023, 12, 31, 23, 59, 59, 0, time.UTC),
			TotalMessages:         rand.Intn(5000) + 1000,
			MessagesSent:          0, // Will be calculated
			MessagesReceived:      0, // Will be calculated
			TotalWords:            rand.Intn(50000) + 10000,
			TotalCharacters:       rand.Intn(250000) + 50000,
			AvgWordsPerMessage:    float64(rand.Intn(10) + 5),
			MostActiveHour:        rand.Intn(24),
			MostActiveDay:         []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"}[rand.Intn(7)],
			AvgResponseTime:       float64(rand.Intn(300000) + 60000), // 1-5 minutes
			PositiveMessages:      rand.Intn(1000) + 500,
			NegativeMessages:      rand.Intn(200) + 50,
			NeutralMessages:       rand.Intn(500) + 200,
			OverallSentiment:      (rand.Float64() - 0.2) * 1.2, // Slightly positive bias
			TotalEmojis:           rand.Intn(1000) + 200,
			UniqueEmojis:          rand.Intn(50) + 20,
			TopEmojis:             `[{"emoji":"😊","count":150},{"emoji":"😂","count":120},{"emoji":"❤️","count":100}]`,
			TopWords:              `[{"word":"great","count":80},{"word":"good","count":75},{"word":"thanks","count":60}]`,
			UniqueWords:           rand.Intn(2000) + 500,
			LongestConversation:   rand.Intn(100) + 20,
			AvgConversationLength: float64(rand.Intn(20) + 5),
			TopContacts:           `[{"name":"Alice Johnson","message_count":250,"relationship_score":85.5}]`,
			NewContacts:           rand.Intn(10) + 2,
			Insights:              "Very active year with lots of positive conversations. Most active during evening hours.",
			Summary:               fmt.Sprintf("Your 2023 Wrapped: %d messages across multiple chats", rand.Intn(5000)+1000),
		}

		wrapped.MessagesSent = wrapped.TotalMessages / 2
		wrapped.MessagesReceived = wrapped.TotalMessages - wrapped.MessagesSent

		wrapped.ID = uuid.New()
		wrapped.CreatedAt = time.Now()
		wrapped.UpdatedAt = time.Now()

		s.db.Create(&wrapped)

		// Create some emoji usage data
		commonEmojis := []string{"😊", "😂", "❤️", "👍", "🎉", "🔥", "💯", "😍"}
		for _, emoji := range commonEmojis {
			usage := entities.EmojiUsage{
				UserID:   user.ID,
				Emoji:    emoji,
				Count:    rand.Intn(100) + 10,
				LastUsed: time.Now().AddDate(0, 0, -rand.Intn(30)),
			}
			usage.ID = uuid.New()
			usage.CreatedAt = time.Now()
			usage.UpdatedAt = time.Now()
			s.db.Create(&usage)
		}

		// Create some word usage data
		commonWords := []string{"great", "good", "thanks", "awesome", "love", "amazing", "perfect", "wonderful"}
		for _, word := range commonWords {
			usage := entities.WordUsage{
				UserID:   user.ID,
				Word:     word,
				Count:    rand.Intn(50) + 5,
				LastUsed: time.Now().AddDate(0, 0, -rand.Intn(30)),
			}
			usage.ID = uuid.New()
			usage.CreatedAt = time.Now()
			usage.UpdatedAt = time.Now()
			s.db.Create(&usage)
		}
	}

	log.Println("✅ Created analytics data")
	return nil
}
