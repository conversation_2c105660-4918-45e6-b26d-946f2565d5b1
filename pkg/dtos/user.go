package dtos

import (
	"time"

	"github.com/google/uuid"
)

// User Registration
type CreateUserReqDto struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Password string `json:"password" validate:"required,min=6"`
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
}

type CreateUserRespDto struct {
	Message string    `json:"message"`
	UserID  uuid.UUID `json:"user_id"`
}

// User Authentication
type AuthenticationRequest struct {
	Username string `json:"username,omitempty"`
	Email    string `json:"email,omitempty"`
	Password string `json:"password" validate:"required"`
}

type AuthenticationResponse struct {
	Token       string    `json:"token"`
	Expires     time.Time `json:"expires"`
	IsSucceeded bool      `json:"is_succeeded"`
	User        UserInfo  `json:"user"`
}

// User Info
type UserInfo struct {
	ID       uuid.UUID `json:"id"`
	Username string    `json:"username"`
	Name     string    `json:"name"`
	Email    string    `json:"email"`
}

// Profile Update
type UpdateProfileRequest struct {
	Name  string `json:"name,omitempty" validate:"omitempty,min=2,max=100"`
	Email string `json:"email,omitempty" validate:"omitempty,email"`
}

type UpdateProfileResponse struct {
	Message string   `json:"message"`
	User    UserInfo `json:"user"`
}

// Change Password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=6"`
}

type ChangePasswordResponse struct {
	Message string `json:"message"`
}

// User Profile
type UserProfileResponse struct {
	User  UserInfo  `json:"user"`
	Stats UserStats `json:"stats"`
}

type UserStats struct {
	TotalChats    int `json:"total_chats"`
	TotalMessages int `json:"total_messages"`
	TotalContacts int `json:"total_contacts"`
	JoinedDays    int `json:"joined_days"`
}
