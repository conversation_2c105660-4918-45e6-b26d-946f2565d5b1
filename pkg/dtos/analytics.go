package dtos

import (
	"time"

	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
)

type AnalyticsRequest struct {
	ChatID       *uuid.UUID            `json:"chat_id,omitempty"`
	AnalysisType entities.AnalysisType `json:"analysis_type" binding:"required,oneof=daily weekly monthly yearly wrapped"`
	StartDate    *time.Time            `json:"start_date,omitempty"`
	EndDate      *time.Time            `json:"end_date,omitempty"`
}

type AnalyticsResponse struct {
	ID           uuid.UUID             `json:"id"`
	ChatID       uuid.UUID             `json:"chat_id"`
	ChatName     string                `json:"chat_name,omitempty"`
	AnalysisType entities.AnalysisType `json:"analysis_type"`
	StartDate    time.Time             `json:"start_date"`
	EndDate      time.Time             `json:"end_date"`

	// Basic statistics
	TotalMessages      int     `json:"total_messages"`
	MessagesSent       int     `json:"messages_sent"`
	MessagesReceived   int     `json:"messages_received"`
	TotalWords         int     `json:"total_words"`
	TotalCharacters    int     `json:"total_characters"`
	AvgWordsPerMessage float64 `json:"avg_words_per_message"`

	// Time analysis
	MostActiveHour  int     `json:"most_active_hour"`
	MostActiveDay   string  `json:"most_active_day"`
	AvgResponseTime float64 `json:"avg_response_time"`

	// Sentiment analysis
	PositiveMessages int     `json:"positive_messages"`
	NegativeMessages int     `json:"negative_messages"`
	NeutralMessages  int     `json:"neutral_messages"`
	OverallSentiment float64 `json:"overall_sentiment"`

	// Emoji analysis
	TotalEmojis  int         `json:"total_emojis"`
	UniqueEmojis int         `json:"unique_emojis"`
	TopEmojis    []EmojiStat `json:"top_emojis"`

	// Word analysis
	TopWords    []WordStat `json:"top_words"`
	UniqueWords int        `json:"unique_words"`

	// Conversation patterns
	LongestConversation   int     `json:"longest_conversation"`
	AvgConversationLength float64 `json:"avg_conversation_length"`

	// Social metrics
	TopContacts []ContactStat `json:"top_contacts"`
	NewContacts int           `json:"new_contacts"`

	// Generated insights
	Insights  string    `json:"insights"`
	Summary   string    `json:"summary"`
	CreatedAt time.Time `json:"created_at"`
}

type EmojiStat struct {
	Emoji string `json:"emoji"`
	Count int    `json:"count"`
}

type WordStat struct {
	Word  string `json:"word"`
	Count int    `json:"count"`
}

type ContactStat struct {
	Name              string  `json:"name"`
	MessageCount      int     `json:"message_count"`
	RelationshipScore float64 `json:"relationship_score"`
}

type WrappedSummaryRequest struct {
	Year int `json:"year" binding:"required,min=2020,max=2030"`
}

type WrappedSummaryResponse struct {
	Year          int `json:"year"`
	TotalMessages int `json:"total_messages"`
	TotalChats    int `json:"total_chats"`
	TotalContacts int `json:"total_contacts"`
	TotalWords    int `json:"total_words"`

	// Top statistics
	TopContact ContactStat `json:"top_contact"`
	TopEmojis  []EmojiStat `json:"top_emojis"`
	TopWords   []WordStat  `json:"top_words"`

	// Time patterns
	MostActiveMonth string `json:"most_active_month"`
	MostActiveDay   string `json:"most_active_day"`
	MostActiveHour  int    `json:"most_active_hour"`

	// Sentiment
	OverallSentiment float64 `json:"overall_sentiment"`
	SentimentLabel   string  `json:"sentiment_label"`

	// Growth metrics
	MessageGrowth float64 `json:"message_growth"` // Compared to previous year
	ContactGrowth int     `json:"contact_growth"`

	// Insights and highlights
	Highlights []string `json:"highlights"`
	Insights   []string `json:"insights"`
	Summary    string   `json:"summary"`

	// Visual data for charts
	MonthlyActivity []MonthlyActivity `json:"monthly_activity"`
	HourlyActivity  []HourlyActivity  `json:"hourly_activity"`
	PlatformStats   []PlatformStat    `json:"platform_stats"`

	GeneratedAt time.Time `json:"generated_at"`
}

type MonthlyActivity struct {
	Month        string `json:"month"`
	MessageCount int    `json:"message_count"`
}

type HourlyActivity struct {
	Hour         int `json:"hour"`
	MessageCount int `json:"message_count"`
}

type PlatformStat struct {
	Platform     entities.ChatPlatform `json:"platform"`
	MessageCount int                   `json:"message_count"`
	ChatCount    int                   `json:"chat_count"`
}

type ChatInsightsResponse struct {
	ChatID   uuid.UUID `json:"chat_id"`
	ChatName string    `json:"chat_name"`

	// Time distribution
	HourlyDistribution map[string]int `json:"hourly_distribution"`
	DailyDistribution  map[string]int `json:"daily_distribution"`

	// Participant analysis
	ContactDistribution map[string]int `json:"contact_distribution"`

	// Communication patterns
	AvgResponseTime      float64               `json:"avg_response_time"`
	ConversationSessions []ConversationSession `json:"conversation_sessions"`

	// Content analysis
	SentimentDistribution   map[string]int `json:"sentiment_distribution"`
	MessageTypeDistribution map[string]int `json:"message_type_distribution"`

	// Engagement metrics
	MostActiveParticipant string `json:"most_active_participant"`
	QuickestResponder     string `json:"quickest_responder"`

	GeneratedAt time.Time `json:"generated_at"`
}

type ConversationSession struct {
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	Duration     int64     `json:"duration"` // in seconds
	MessageCount int       `json:"message_count"`
}

type SocialNetworkResponse struct {
	UserID            uuid.UUID           `json:"user_id"`
	Networks          []SocialNetworkNode `json:"networks"`
	TotalContacts     int                 `json:"total_contacts"`
	StrongConnections int                 `json:"strong_connections"` // Score > 70
	WeakConnections   int                 `json:"weak_connections"`   // Score < 30
	GeneratedAt       time.Time           `json:"generated_at"`
}

type SocialNetworkNode struct {
	ContactID   uuid.UUID             `json:"contact_id"`
	ContactName string                `json:"contact_name"`
	Platform    entities.ChatPlatform `json:"platform"`

	// Relationship metrics
	MessageExchanges int     `json:"message_exchanges"`
	ResponseRate     float64 `json:"response_rate"`
	InitiationRate   float64 `json:"initiation_rate"`
	AvgResponseTime  float64 `json:"avg_response_time"`

	// Interaction patterns
	LastInteraction time.Time `json:"last_interaction"`
	InteractionDays int       `json:"interaction_days"`
	StreakDays      int       `json:"streak_days"`

	// Relationship strength
	RelationshipScore float64 `json:"relationship_score"`
	TrendDirection    string  `json:"trend_direction"`

	// Communication style
	AvgMessageLength float64 `json:"avg_message_length"`
	EmojiUsageRate   float64 `json:"emoji_usage_rate"`
	SentimentMatch   float64 `json:"sentiment_match"`
}

type AnalyticsListResponse struct {
	Analyses []AnalyticsResponse `json:"analyses"`
	Total    int                 `json:"total"`
}

type EmojiAnalysisResponse struct {
	ChatID       *uuid.UUID  `json:"chat_id,omitempty"`
	TotalEmojis  int         `json:"total_emojis"`
	UniqueEmojis int         `json:"unique_emojis"`
	TopEmojis    []EmojiStat `json:"top_emojis"`
	GeneratedAt  time.Time   `json:"generated_at"`
}

type WordAnalysisResponse struct {
	ChatID      *uuid.UUID `json:"chat_id,omitempty"`
	TotalWords  int        `json:"total_words"`
	UniqueWords int        `json:"unique_words"`
	TopWords    []WordStat `json:"top_words"`
	GeneratedAt time.Time  `json:"generated_at"`
}
