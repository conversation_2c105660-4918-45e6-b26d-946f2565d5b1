package dtos

import (
	"mime/multipart"
	"time"

	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
)

type ChatUploadRequest struct {
	File     *multipart.FileHeader `form:"file" binding:"required"`
	Platform string                `form:"platform" binding:"required,oneof=whatsapp telegram messenger imessage discord other"`
	ChatName string                `form:"chat_name"`
}

type ChatUploadResponse struct {
	ChatID      uuid.UUID `json:"chat_id"`
	Message     string    `json:"message"`
	ProcessedAt time.Time `json:"processed_at"`
	Stats       ChatStats `json:"stats"`
}

type ChatStats struct {
	TotalMessages    int       `json:"total_messages"`
	TotalContacts    int       `json:"total_contacts"`
	DateRange        DateRange `json:"date_range"`
	ProcessingTimeMs int64     `json:"processing_time_ms"`
}

type DateRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

type WhatsAppMessage struct {
	Timestamp string `json:"timestamp"`
	Sender    string `json:"sender"`
	Message   string `json:"message"`
	Type      string `json:"type,omitempty"`
}

type TelegramMessage struct {
	ID     int64     `json:"id"`
	Date   time.Time `json:"date"`
	From   string    `json:"from"`
	Text   string    `json:"text"`
	Type   string    `json:"type"`
	FromID string    `json:"from_id,omitempty"`
}

type DiscordMessage struct {
	ID        string    `json:"id"`
	Timestamp time.Time `json:"timestamp"`
	Author    Author    `json:"author"`
	Content   string    `json:"content"`
	Type      int       `json:"type"`
}

type Author struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}

type ChatListResponse struct {
	Chats []ChatSummary `json:"chats"`
	Total int           `json:"total"`
}

type ChatSummary struct {
	ID               uuid.UUID             `json:"id"`
	Name             string                `json:"name"`
	Platform         entities.ChatPlatform `json:"platform"`
	IsGroup          bool                  `json:"is_group"`
	TotalMessages    int                   `json:"total_messages"`
	FirstMessageAt   time.Time             `json:"first_message_at"`
	LastMessageAt    time.Time             `json:"last_message_at"`
	IsAnalyzed       bool                  `json:"is_analyzed"`
	ParticipantCount int                   `json:"participant_count"`
	CreatedAt        time.Time             `json:"created_at"`
}

type ChatDetailResponse struct {
	Chat           ChatSummary      `json:"chat"`
	Participants   []ContactSummary `json:"participants"`
	RecentMessages []MessageSummary `json:"recent_messages"`
}

type ContactSummary struct {
	ID                uuid.UUID             `json:"id"`
	Name              string                `json:"name"`
	Platform          entities.ChatPlatform `json:"platform"`
	TotalMessages     int                   `json:"total_messages"`
	MessagesSent      int                   `json:"messages_sent"`
	MessagesReceived  int                   `json:"messages_received"`
	RelationshipScore float64               `json:"relationship_score"`
}

type MessageSummary struct {
	ID          uuid.UUID            `json:"id"`
	Content     string               `json:"content"`
	MessageType entities.MessageType `json:"message_type"`
	Timestamp   time.Time            `json:"timestamp"`
	IsFromUser  bool                 `json:"is_from_user"`
	ContactName string               `json:"contact_name,omitempty"`
	WordCount   int                  `json:"word_count"`
	HasEmoji    bool                 `json:"has_emoji"`
}

type ProcessingStatus struct {
	ChatID    uuid.UUID `json:"chat_id"`
	Status    string    `json:"status"`   // "processing", "completed", "failed"
	Progress  int       `json:"progress"` // 0-100
	Message   string    `json:"message"`
	StartedAt time.Time `json:"started_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type BulkUploadRequest struct {
	Files    []*multipart.FileHeader `form:"files" binding:"required"`
	Platform string                  `form:"platform" binding:"required"`
}

type BulkUploadResponse struct {
	TotalFiles     int                  `json:"total_files"`
	ProcessedFiles int                  `json:"processed_files"`
	FailedFiles    int                  `json:"failed_files"`
	Results        []ChatUploadResponse `json:"results"`
	Errors         []string             `json:"errors,omitempty"`
}
