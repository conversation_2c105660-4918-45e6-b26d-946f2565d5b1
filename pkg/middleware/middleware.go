package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-base-project-structure/pkg/config"
	"github.com/go-base-project-structure/pkg/database"
	"github.com/go-base-project-structure/pkg/domains/auth"
	"github.com/go-base-project-structure/pkg/localizer"
	"github.com/go-base-project-structure/pkg/state"
	"github.com/go-base-project-structure/pkg/utils"
	"github.com/google/uuid"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func FromClient() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("client_id")
		if client_id == config.InitConfig().App.ClientID {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.InitConfig().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)

			// Parse user ID to UUID
			userID, err := uuid.Parse(claims.UserID)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID in token"})
				return
			}

			// Set context values
			c.Set("user_id", userID)                  // UUID for handlers
			c.Set(state.CurrentUserID, claims.UserID) // String for state
			c.Set(state.CurrentDeviceID, claims.DeviceID)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentTimezone, claims.Timezone)
			c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

// DemoAuth provides demo authentication for development/testing
func DemoAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Demo user IDs from seeder
		// Demo user credentials - will lookup real user IDs from database
		demoCredentials := map[string]string{
			"demo_token_123":  "<EMAIL>",
			"test_token_456":  "<EMAIL>",
			"admin_token_789": "<EMAIL>",
		}

		// Get token from Authorization header
		bearer := c.Request.Header.Get("Authorization")
		var token string

		if bearer != "" && strings.HasPrefix(bearer, "Bearer ") {
			token = strings.TrimPrefix(bearer, "Bearer ")
		} else {
			// Also check for direct token in header or query
			token = c.GetHeader("X-Auth-Token")
			if token == "" {
				token = c.Query("token")
			}
		}

		// If no token provided, use default demo user
		if token == "" {
			token = "demo_token_123"
		}

		// Check if token exists in demo credentials
		if email, exists := demoCredentials[token]; exists {
			// Get user from database by email
			authRepo := auth.NewRepo(database.DBClient())
			user, err := authRepo.GetUserByEmail(email)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
					"error": "Demo user not found in database",
				})
				return
			}

			// Set user context
			c.Set("user_id", user.ID)
			c.Set(state.CurrentUserID, user.ID.String())
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentDeviceID, "demo_device")
			c.Set(state.CurrentTimezone, "UTC")
			c.Set(state.CurrentPhoneLanguage, "en")

			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":            "Invalid demo token. Use: demo_token_123, test_token_456, or admin_token_789",
				"available_tokens": []string{"demo_token_123", "test_token_456", "admin_token_789"},
			})
			return
		}
	}
}

// OptionalDemoAuth provides optional demo authentication (doesn't block if no token)
func OptionalDemoAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Demo user credentials
		demoCredentials := map[string]string{
			"demo_token_123":  "<EMAIL>",
			"test_token_456":  "<EMAIL>",
			"admin_token_789": "<EMAIL>",
		}

		// Get token
		bearer := c.Request.Header.Get("Authorization")
		var token string

		if bearer != "" && strings.HasPrefix(bearer, "Bearer ") {
			token = strings.TrimPrefix(bearer, "Bearer ")
		} else {
			token = c.GetHeader("X-Auth-Token")
			if token == "" {
				token = c.Query("token")
			}
		}

		// Set user if token is valid, otherwise continue without user
		if token != "" {
			if email, exists := demoCredentials[token]; exists {
				// Get user from database by email
				authRepo := auth.NewRepo(database.DBClient())
				user, err := authRepo.GetUserByEmail(email)
				if err == nil {
					c.Set("user_id", user.ID)
					c.Set(state.CurrentUserID, user.ID.String())
					c.Set(state.CurrentUserIP, c.ClientIP())
					c.Set(state.CurrentDeviceID, "demo_device")
					c.Set(state.CurrentTimezone, "UTC")
					c.Set(state.CurrentPhoneLanguage, "en")
				}
			}
		}

		c.Next()
	}
}

// JWTAuth is an alias for Authorized middleware for consistency
func JWTAuth() gin.HandlerFunc {
	return Authorized()
}
