package sentiment

import (
	"github.com/go-base-project-structure/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	CreateSentimentWords(words []entities.SentimentWord) error
	GetSentimentWordsByLanguage(language string) ([]entities.SentimentWord, error)
	GetSentimentWordsBySentiment(language string, sentiment entities.SentimentType) ([]entities.SentimentWord, error)
	ClearSentimentWords(language string) error
	GetSentimentWordsCount(language string) (int64, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateSentimentWords(words []entities.SentimentWord) error {
	return r.db.CreateInBatches(words, 1000).Error
}

func (r *repository) GetSentimentWordsByLanguage(language string) ([]entities.SentimentWord, error) {
	var words []entities.SentimentWord
	err := r.db.Where("language = ?", language).Find(&words).Error
	return words, err
}

func (r *repository) GetSentimentWordsBySentiment(language string, sentiment entities.SentimentType) ([]entities.SentimentWord, error) {
	var words []entities.SentimentWord
	err := r.db.Where("language = ? AND sentiment = ?", language, sentiment).Find(&words).Error
	return words, err
}

func (r *repository) ClearSentimentWords(language string) error {
	return r.db.Where("language = ?", language).Delete(&entities.SentimentWord{}).Error
}

func (r *repository) GetSentimentWordsCount(language string) (int64, error) {
	var count int64
	err := r.db.Model(&entities.SentimentWord{}).Where("language = ?", language).Count(&count).Error
	return count, err
}
