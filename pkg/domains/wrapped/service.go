package wrapped

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/go-base-project-structure/pkg/domains/analytics"
	"github.com/go-base-project-structure/pkg/domains/chat"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	GenerateWrappedSummary(userID uuid.UUID, year int) (*dtos.WrappedSummaryResponse, error)
	GetWrappedHistory(userID uuid.UUID) ([]dtos.WrappedSummaryResponse, error)
	GenerateMonthlyWrapped(userID uuid.UUID, year int, month int) (*dtos.WrappedSummaryResponse, error)
	GetWrappedInsights(userID uuid.UUID, year int) ([]string, error)
}

type service struct {
	chatRepo         chat.Repository
	analyticsRepo    analytics.Repository
	analyticsService analytics.Service
}

func NewService(chatRepo chat.Repository, analyticsRepo analytics.Repository, analyticsService analytics.Service) Service {
	return &service{
		chatRepo:         chatRepo,
		analyticsRepo:    analyticsRepo,
		analyticsService: analyticsService,
	}
}

func (s *service) GenerateWrappedSummary(userID uuid.UUID, year int) (*dtos.WrappedSummaryResponse, error) {
	startDate := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(year, 12, 31, 23, 59, 59, 0, time.UTC)

	// Get all user chats
	chats, err := s.chatRepo.GetChatsByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user chats: %w", err)
	}

	// Aggregate data from all chats
	totalMessages := 0
	totalWords := 0
	allMessages := []entities.Message{}
	contactStats := make(map[string]*dtos.ContactStat)
	emojiCounts := make(map[string]int)
	wordCounts := make(map[string]int)
	monthlyActivity := make(map[string]int)
	hourlyActivity := make(map[int]int)
	platformStats := make(map[entities.ChatPlatform]*dtos.PlatformStat)

	for _, chat := range chats {
		messages, err := s.chatRepo.GetMessagesByDateRange(chat.ID, startDate, endDate)
		if err != nil {
			continue
		}

		totalMessages += len(messages)
		allMessages = append(allMessages, messages...)

		// Platform statistics
		if platformStats[chat.Platform] == nil {
			platformStats[chat.Platform] = &dtos.PlatformStat{
				Platform: chat.Platform,
			}
		}
		platformStats[chat.Platform].MessageCount += len(messages)
		platformStats[chat.Platform].ChatCount++

		// Process messages for various analytics
		for _, message := range messages {
			totalWords += message.WordCount

			// Monthly activity
			monthKey := message.Timestamp.Format("2006-01")
			monthlyActivity[monthKey]++

			// Hourly activity
			hourlyActivity[message.Timestamp.Hour()]++

			// Contact statistics
			if message.ContactID != nil {
				// Get contact info separately since we removed foreign keys
				contact, err := s.chatRepo.GetContactByID(*message.ContactID)
				if err == nil {
					contactName := contact.Name
					if contactStats[contactName] == nil {
						contactStats[contactName] = &dtos.ContactStat{
							Name: contactName,
						}
					}
					contactStats[contactName].MessageCount++
				}
			}

			// Emoji analysis
			s.extractEmojis(message.Content, emojiCounts)

			// Word analysis
			s.extractWords(message.Content, wordCounts)
		}
	}

	// Calculate sentiment
	overallSentiment := s.calculateOverallSentiment(allMessages)
	sentimentLabel := s.getSentimentLabel(overallSentiment)

	// Get top statistics
	topContact := s.getTopContact(contactStats)
	topEmojis := s.getTopEmojis(emojiCounts, 10)
	topWords := s.getTopWords(wordCounts, 20)

	// Time patterns
	mostActiveMonth := s.getMostActiveMonth(monthlyActivity)
	mostActiveDay := s.getMostActiveDay(allMessages)
	mostActiveHour := s.getMostActiveHour(hourlyActivity)

	// Growth metrics (compare with previous year)
	messageGrowth := s.calculateMessageGrowth(userID, year)
	contactGrowth := s.calculateContactGrowth(userID, year)

	// Generate insights and highlights
	highlights := s.generateHighlights(totalMessages, totalWords, len(chats), len(contactStats), topContact)
	insights := s.generateInsights(overallSentiment, mostActiveDay, mostActiveHour, messageGrowth)
	summary := s.generateSummary(year, totalMessages, len(chats), topContact.Name)

	// Convert monthly activity to slice
	monthlyActivitySlice := s.convertMonthlyActivity(monthlyActivity)
	hourlyActivitySlice := s.convertHourlyActivity(hourlyActivity)
	platformStatsSlice := s.convertPlatformStats(platformStats)

	response := &dtos.WrappedSummaryResponse{
		Year:             year,
		TotalMessages:    totalMessages,
		TotalChats:       len(chats),
		TotalContacts:    len(contactStats),
		TotalWords:       totalWords,
		TopContact:       topContact,
		TopEmojis:        topEmojis,
		TopWords:         topWords,
		MostActiveMonth:  mostActiveMonth,
		MostActiveDay:    mostActiveDay,
		MostActiveHour:   mostActiveHour,
		OverallSentiment: overallSentiment,
		SentimentLabel:   sentimentLabel,
		MessageGrowth:    messageGrowth,
		ContactGrowth:    contactGrowth,
		Highlights:       highlights,
		Insights:         insights,
		Summary:          summary,
		MonthlyActivity:  monthlyActivitySlice,
		HourlyActivity:   hourlyActivitySlice,
		PlatformStats:    platformStatsSlice,
		GeneratedAt:      time.Now(),
	}

	// Save wrapped analysis to database
	analysis := &entities.ChatAnalysis{
		UserID:           userID,
		AnalysisType:     entities.WrappedAnalysis,
		StartDate:        startDate,
		EndDate:          endDate,
		TotalMessages:    totalMessages,
		TotalWords:       totalWords,
		OverallSentiment: overallSentiment,
		MostActiveDay:    mostActiveDay,
		MostActiveHour:   mostActiveHour,
		Summary:          summary,
		Insights:         fmt.Sprintf("%v", insights),
	}

	if topEmojisJSON, err := json.Marshal(topEmojis); err == nil {
		analysis.TopEmojis = string(topEmojisJSON)
	}
	if topWordsJSON, err := json.Marshal(topWords); err == nil {
		analysis.TopWords = string(topWordsJSON)
	}
	if topContactsJSON, err := json.Marshal([]dtos.ContactStat{topContact}); err == nil {
		analysis.TopContacts = string(topContactsJSON)
	}

	s.analyticsRepo.CreateAnalysis(analysis)

	return response, nil
}

func (s *service) GetWrappedHistory(userID uuid.UUID) ([]dtos.WrappedSummaryResponse, error) {
	analyses, err := s.analyticsRepo.GetAnalysesByUserID(userID, entities.WrappedAnalysis)
	if err != nil {
		return nil, err
	}

	var wrappedSummaries []dtos.WrappedSummaryResponse
	for _, analysis := range analyses {
		year := analysis.StartDate.Year()

		// Parse stored JSON data
		var topEmojis []dtos.EmojiStat
		var topWords []dtos.WordStat
		var topContacts []dtos.ContactStat

		json.Unmarshal([]byte(analysis.TopEmojis), &topEmojis)
		json.Unmarshal([]byte(analysis.TopWords), &topWords)
		json.Unmarshal([]byte(analysis.TopContacts), &topContacts)

		var topContact dtos.ContactStat
		if len(topContacts) > 0 {
			topContact = topContacts[0]
		}

		wrapped := dtos.WrappedSummaryResponse{
			Year:             year,
			TotalMessages:    analysis.TotalMessages,
			TotalWords:       analysis.TotalWords,
			TopContact:       topContact,
			TopEmojis:        topEmojis,
			TopWords:         topWords,
			MostActiveDay:    analysis.MostActiveDay,
			MostActiveHour:   analysis.MostActiveHour,
			OverallSentiment: analysis.OverallSentiment,
			SentimentLabel:   s.getSentimentLabel(analysis.OverallSentiment),
			Summary:          analysis.Summary,
			GeneratedAt:      analysis.CreatedAt,
		}

		wrappedSummaries = append(wrappedSummaries, wrapped)
	}

	// Sort by year descending
	sort.Slice(wrappedSummaries, func(i, j int) bool {
		return wrappedSummaries[i].Year > wrappedSummaries[j].Year
	})

	return wrappedSummaries, nil
}

func (s *service) GenerateMonthlyWrapped(userID uuid.UUID, year int, month int) (*dtos.WrappedSummaryResponse, error) {
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	endDate := startDate.AddDate(0, 1, -1).Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	// Similar logic to yearly wrapped but for a specific month
	chats, err := s.chatRepo.GetChatsByUserID(userID)
	if err != nil {
		return nil, err
	}

	totalMessages := 0
	totalWords := 0
	allMessages := []entities.Message{}

	for _, chat := range chats {
		messages, err := s.chatRepo.GetMessagesByDateRange(chat.ID, startDate, endDate)
		if err != nil {
			continue
		}

		totalMessages += len(messages)
		allMessages = append(allMessages, messages...)

		for _, message := range messages {
			totalWords += message.WordCount
		}
	}

	monthName := startDate.Format("January")
	summary := fmt.Sprintf("Your %s %d: %d messages across %d chats", monthName, year, totalMessages, len(chats))

	return &dtos.WrappedSummaryResponse{
		Year:          year,
		TotalMessages: totalMessages,
		TotalChats:    len(chats),
		TotalWords:    totalWords,
		Summary:       summary,
		GeneratedAt:   time.Now(),
	}, nil
}

func (s *service) GetWrappedInsights(userID uuid.UUID, year int) ([]string, error) {
	// Get wrapped analysis for the year
	analyses, err := s.analyticsRepo.GetAnalysesByUserID(userID, entities.WrappedAnalysis)
	if err != nil {
		return nil, err
	}

	for _, analysis := range analyses {
		if analysis.StartDate.Year() == year {
			var insights []string
			if err := json.Unmarshal([]byte(analysis.Insights), &insights); err == nil {
				return insights, nil
			}
		}
	}

	return []string{}, nil
}

// Helper functions
func (s *service) extractEmojis(content string, emojiCounts map[string]int) {
	// Simple emoji extraction - in production, use a proper emoji library
	emojis := []string{"😀", "😂", "❤️", "👍", "😊", "🔥", "💯", "😍", "🎉", "👏"}
	for _, emoji := range emojis {
		if count := len(content) - len(strings.ReplaceAll(content, emoji, "")); count > 0 {
			emojiCounts[emoji] += count / len(emoji)
		}
	}
}

func (s *service) extractWords(content string, wordCounts map[string]int) {
	words := strings.Fields(strings.ToLower(content))
	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true, "but": true,
		"in": true, "on": true, "at": true, "to": true, "for": true, "of": true,
	}

	for _, word := range words {
		word = strings.Trim(word, ".,!?;:")
		if len(word) > 2 && !stopWords[word] {
			wordCounts[word]++
		}
	}
}

func (s *service) calculateOverallSentiment(messages []entities.Message) float64 {
	if len(messages) == 0 {
		return 0
	}

	totalSentiment := 0.0
	for _, message := range messages {
		totalSentiment += message.SentimentScore
	}

	return totalSentiment / float64(len(messages))
}

func (s *service) getSentimentLabel(sentiment float64) string {
	if sentiment > 0.1 {
		return "Positive"
	} else if sentiment < -0.1 {
		return "Negative"
	}
	return "Neutral"
}

func (s *service) getTopContact(contactStats map[string]*dtos.ContactStat) dtos.ContactStat {
	var topContact dtos.ContactStat
	maxMessages := 0

	for _, contact := range contactStats {
		if contact.MessageCount > maxMessages {
			maxMessages = contact.MessageCount
			topContact = *contact
		}
	}

	return topContact
}

func (s *service) getTopEmojis(emojiCounts map[string]int, limit int) []dtos.EmojiStat {
	var emojis []dtos.EmojiStat
	for emoji, count := range emojiCounts {
		emojis = append(emojis, dtos.EmojiStat{Emoji: emoji, Count: count})
	}

	sort.Slice(emojis, func(i, j int) bool {
		return emojis[i].Count > emojis[j].Count
	})

	if len(emojis) > limit {
		emojis = emojis[:limit]
	}

	return emojis
}

func (s *service) getTopWords(wordCounts map[string]int, limit int) []dtos.WordStat {
	var words []dtos.WordStat
	for word, count := range wordCounts {
		if count > 1 { // Only include words used more than once
			words = append(words, dtos.WordStat{Word: word, Count: count})
		}
	}

	sort.Slice(words, func(i, j int) bool {
		return words[i].Count > words[j].Count
	})

	if len(words) > limit {
		words = words[:limit]
	}

	return words
}

func (s *service) getMostActiveMonth(monthlyActivity map[string]int) string {
	maxCount := 0
	mostActiveMonth := ""

	for month, count := range monthlyActivity {
		if count > maxCount {
			maxCount = count
			mostActiveMonth = month
		}
	}

	if mostActiveMonth != "" {
		if t, err := time.Parse("2006-01", mostActiveMonth); err == nil {
			return t.Format("January")
		}
	}

	return "Unknown"
}

func (s *service) getMostActiveDay(messages []entities.Message) string {
	dayCounts := make(map[string]int)
	days := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}

	for _, message := range messages {
		day := days[message.Timestamp.Weekday()]
		dayCounts[day]++
	}

	maxCount := 0
	mostActiveDay := ""
	for day, count := range dayCounts {
		if count > maxCount {
			maxCount = count
			mostActiveDay = day
		}
	}

	return mostActiveDay
}

func (s *service) getMostActiveHour(hourlyActivity map[int]int) int {
	maxCount := 0
	mostActiveHour := 0

	for hour, count := range hourlyActivity {
		if count > maxCount {
			maxCount = count
			mostActiveHour = hour
		}
	}

	return mostActiveHour
}

func (s *service) calculateMessageGrowth(userID uuid.UUID, year int) float64 {
	// Get previous year's data
	prevYearAnalyses, err := s.analyticsRepo.GetAnalysesByUserID(userID, entities.WrappedAnalysis)
	if err != nil {
		return 0
	}

	var currentYearMessages, prevYearMessages int
	for _, analysis := range prevYearAnalyses {
		if analysis.StartDate.Year() == year {
			currentYearMessages = analysis.TotalMessages
		} else if analysis.StartDate.Year() == year-1 {
			prevYearMessages = analysis.TotalMessages
		}
	}

	if prevYearMessages == 0 {
		return 0
	}

	return float64(currentYearMessages-prevYearMessages) / float64(prevYearMessages) * 100
}

func (s *service) calculateContactGrowth(userID uuid.UUID, year int) int {
	// Simple implementation - count new contacts this year
	// In a real implementation, you'd track when contacts were first added
	return 0
}

func (s *service) generateHighlights(totalMessages, totalWords, totalChats, totalContacts int, topContact dtos.ContactStat) []string {
	highlights := []string{}

	if totalMessages > 1000 {
		highlights = append(highlights, fmt.Sprintf("🎉 You sent over %d messages this year!", totalMessages))
	}

	if totalWords > 10000 {
		highlights = append(highlights, fmt.Sprintf("📚 You wrote %d words - that's like a short book!", totalWords))
	}

	if totalChats > 10 {
		highlights = append(highlights, fmt.Sprintf("💬 You were active in %d different chats", totalChats))
	}

	if topContact.Name != "" {
		highlights = append(highlights, fmt.Sprintf("👥 %s was your most frequent chat partner with %d messages", topContact.Name, topContact.MessageCount))
	}

	return highlights
}

func (s *service) generateInsights(sentiment float64, mostActiveDay string, mostActiveHour int, messageGrowth float64) []string {
	insights := []string{}

	if sentiment > 0.1 {
		insights = append(insights, "😊 Your conversations were mostly positive this year")
	} else if sentiment < -0.1 {
		insights = append(insights, "😔 Your conversations had some negative moments this year")
	}

	insights = append(insights, fmt.Sprintf("📅 You were most active on %s", mostActiveDay))

	if mostActiveHour >= 9 && mostActiveHour <= 17 {
		insights = append(insights, "💼 You chat most during work hours")
	} else if mostActiveHour >= 22 || mostActiveHour <= 6 {
		insights = append(insights, "🌙 You're a night owl - most active during late hours")
	}

	if messageGrowth > 20 {
		insights = append(insights, fmt.Sprintf("📈 Your messaging increased by %.1f%% compared to last year", messageGrowth))
	} else if messageGrowth < -20 {
		insights = append(insights, fmt.Sprintf("📉 Your messaging decreased by %.1f%% compared to last year", -messageGrowth))
	}

	return insights
}

func (s *service) generateSummary(year, totalMessages, totalChats int, topContactName string) string {
	return fmt.Sprintf("Your %d Wrapped: %d messages across %d chats. Your top contact was %s.",
		year, totalMessages, totalChats, topContactName)
}

func (s *service) convertMonthlyActivity(monthlyActivity map[string]int) []dtos.MonthlyActivity {
	var activity []dtos.MonthlyActivity
	months := []string{"01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"}
	monthNames := []string{"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"}

	for i, month := range months {
		key := fmt.Sprintf("2023-%s", month) // Use current year
		count := monthlyActivity[key]
		activity = append(activity, dtos.MonthlyActivity{
			Month:        monthNames[i],
			MessageCount: count,
		})
	}

	return activity
}

func (s *service) convertHourlyActivity(hourlyActivity map[int]int) []dtos.HourlyActivity {
	var activity []dtos.HourlyActivity
	for hour := 0; hour < 24; hour++ {
		activity = append(activity, dtos.HourlyActivity{
			Hour:         hour,
			MessageCount: hourlyActivity[hour],
		})
	}
	return activity
}

func (s *service) convertPlatformStats(platformStats map[entities.ChatPlatform]*dtos.PlatformStat) []dtos.PlatformStat {
	var stats []dtos.PlatformStat
	for _, stat := range platformStats {
		stats = append(stats, *stat)
	}

	sort.Slice(stats, func(i, j int) bool {
		return stats[i].MessageCount > stats[j].MessageCount
	})

	return stats
}
