package social

import (
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/go-base-project-structure/pkg/domains/analytics"
	"github.com/go-base-project-structure/pkg/domains/chat"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	AnalyzeSocialNetwork(userID uuid.UUID) (*dtos.SocialNetworkResponse, error)
	GetContactRelationship(userID, contactID uuid.UUID) (*dtos.SocialNetworkNode, error)
	GetStrongestConnections(userID uuid.UUID, limit int) ([]dtos.SocialNetworkNode, error)
	GetWeakestConnections(userID uuid.UUID, limit int) ([]dtos.SocialNetworkNode, error)
	UpdateRelationshipScores(userID uuid.UUID) error
	GetRelationshipTrends(userID uuid.UUID) (map[string]interface{}, error)
}

type service struct {
	chatRepo      chat.Repository
	analyticsRepo analytics.Repository
}

func NewService(chatRepo chat.Repository, analyticsRepo analytics.Repository) Service {
	return &service{
		chatRepo:      chatRepo,
		analyticsRepo: analyticsRepo,
	}
}

func (s *service) AnalyzeSocialNetwork(userID uuid.UUID) (*dtos.SocialNetworkResponse, error) {
	// Get all user chats
	chats, err := s.chatRepo.GetChatsByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user chats: %w", err)
	}

	// Get all user contacts
	contacts, err := s.chatRepo.GetContactsByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user contacts: %w", err)
	}

	// Analyze each contact relationship
	var networkNodes []dtos.SocialNetworkNode
	contactMetrics := make(map[uuid.UUID]*ContactMetrics)

	// Initialize contact metrics
	for _, contact := range contacts {
		contactMetrics[contact.ID] = &ContactMetrics{
			Contact:          contact,
			MessagesSent:     0,
			MessagesReceived: 0,
			ResponseTimes:    []float64{},
			InitiatedChats:   0,
			LastInteraction:  time.Time{},
			InteractionDays:  make(map[string]bool),
			MessageLengths:   []int{},
			EmojiCount:       0,
			TotalMessages:    0,
		}
	}

	// Analyze messages for each chat
	for _, chat := range chats {
		messages, err := s.chatRepo.GetMessagesByChatID(chat.ID, 10000, 0) // Get all messages
		if err != nil {
			continue
		}

		s.analyzeMessagesForSocialNetwork(messages, contactMetrics)
	}

	// Calculate relationship scores and create network nodes
	strongConnections := 0
	weakConnections := 0

	for contactID, metrics := range contactMetrics {
		if metrics.TotalMessages == 0 {
			continue // Skip contacts with no messages
		}

		node := s.calculateRelationshipNode(metrics)
		networkNodes = append(networkNodes, node)

		// Save to database
		socialNetwork := &entities.SocialNetwork{
			UserID:            userID,
			ContactID:         contactID,
			MessageExchanges:  metrics.TotalMessages,
			ResponseRate:      node.ResponseRate,
			InitiationRate:    node.InitiationRate,
			AvgResponseTime:   node.AvgResponseTime,
			LastInteraction:   node.LastInteraction,
			InteractionDays:   len(metrics.InteractionDays),
			RelationshipScore: node.RelationshipScore,
			TrendDirection:    node.TrendDirection,
			AvgMessageLength:  node.AvgMessageLength,
			EmojiUsageRate:    node.EmojiUsageRate,
		}

		s.analyticsRepo.CreateSocialNetwork(socialNetwork)

		// Count connection strengths
		if node.RelationshipScore > 70 {
			strongConnections++
		} else if node.RelationshipScore < 30 {
			weakConnections++
		}
	}

	// Sort by relationship score
	sort.Slice(networkNodes, func(i, j int) bool {
		return networkNodes[i].RelationshipScore > networkNodes[j].RelationshipScore
	})

	return &dtos.SocialNetworkResponse{
		UserID:            userID,
		Networks:          networkNodes,
		TotalContacts:     len(networkNodes),
		StrongConnections: strongConnections,
		WeakConnections:   weakConnections,
		GeneratedAt:       time.Now(),
	}, nil
}

func (s *service) GetContactRelationship(userID, contactID uuid.UUID) (*dtos.SocialNetworkNode, error) {
	// Get existing social network data
	networks, err := s.analyticsRepo.GetSocialNetworkByUser(userID)
	if err != nil {
		return nil, err
	}

	for _, network := range networks {
		if network.ContactID == contactID {
			// Get contact info separately
			contact, err := s.chatRepo.GetContactByID(network.ContactID)
			if err != nil {
				continue
			}

			return &dtos.SocialNetworkNode{
				ContactID:         network.ContactID,
				ContactName:       contact.Name,
				Platform:          contact.Platform,
				MessageExchanges:  network.MessageExchanges,
				ResponseRate:      network.ResponseRate,
				InitiationRate:    network.InitiationRate,
				AvgResponseTime:   network.AvgResponseTime,
				LastInteraction:   network.LastInteraction,
				InteractionDays:   network.InteractionDays,
				RelationshipScore: network.RelationshipScore,
				TrendDirection:    network.TrendDirection,
				AvgMessageLength:  network.AvgMessageLength,
				EmojiUsageRate:    network.EmojiUsageRate,
				SentimentMatch:    network.SentimentMatch,
			}, nil
		}
	}

	return nil, fmt.Errorf("relationship not found")
}

func (s *service) GetStrongestConnections(userID uuid.UUID, limit int) ([]dtos.SocialNetworkNode, error) {
	networks, err := s.analyticsRepo.GetSocialNetworkByUser(userID)
	if err != nil {
		return nil, err
	}

	var nodes []dtos.SocialNetworkNode
	for _, network := range networks {
		if network.RelationshipScore > 70 {
			// Get contact info separately
			contact, err := s.chatRepo.GetContactByID(network.ContactID)
			if err != nil {
				continue
			}

			node := dtos.SocialNetworkNode{
				ContactID:         network.ContactID,
				ContactName:       contact.Name,
				Platform:          contact.Platform,
				MessageExchanges:  network.MessageExchanges,
				ResponseRate:      network.ResponseRate,
				RelationshipScore: network.RelationshipScore,
				LastInteraction:   network.LastInteraction,
			}
			nodes = append(nodes, node)
		}
	}

	if len(nodes) > limit {
		nodes = nodes[:limit]
	}

	return nodes, nil
}

func (s *service) GetWeakestConnections(userID uuid.UUID, limit int) ([]dtos.SocialNetworkNode, error) {
	networks, err := s.analyticsRepo.GetSocialNetworkByUser(userID)
	if err != nil {
		return nil, err
	}

	var nodes []dtos.SocialNetworkNode
	for _, network := range networks {
		if network.RelationshipScore < 30 {
			// Get contact info separately
			contact, err := s.chatRepo.GetContactByID(network.ContactID)
			if err != nil {
				continue
			}

			node := dtos.SocialNetworkNode{
				ContactID:         network.ContactID,
				ContactName:       contact.Name,
				Platform:          contact.Platform,
				MessageExchanges:  network.MessageExchanges,
				ResponseRate:      network.ResponseRate,
				RelationshipScore: network.RelationshipScore,
				LastInteraction:   network.LastInteraction,
			}
			nodes = append(nodes, node)
		}
	}

	// Sort by lowest score first
	sort.Slice(nodes, func(i, j int) bool {
		return nodes[i].RelationshipScore < nodes[j].RelationshipScore
	})

	if len(nodes) > limit {
		nodes = nodes[:limit]
	}

	return nodes, nil
}

func (s *service) UpdateRelationshipScores(userID uuid.UUID) error {
	// Re-analyze social network to update scores
	_, err := s.AnalyzeSocialNetwork(userID)
	return err
}

func (s *service) GetRelationshipTrends(userID uuid.UUID) (map[string]interface{}, error) {
	networks, err := s.analyticsRepo.GetSocialNetworkByUser(userID)
	if err != nil {
		return nil, err
	}

	trends := make(map[string]interface{})

	// Count trends
	increasing := 0
	decreasing := 0
	stable := 0

	for _, network := range networks {
		switch network.TrendDirection {
		case "increasing":
			increasing++
		case "decreasing":
			decreasing++
		case "stable":
			stable++
		}
	}

	trends["increasing_relationships"] = increasing
	trends["decreasing_relationships"] = decreasing
	trends["stable_relationships"] = stable
	trends["total_relationships"] = len(networks)

	// Calculate average relationship score
	totalScore := 0.0
	for _, network := range networks {
		totalScore += network.RelationshipScore
	}
	if len(networks) > 0 {
		trends["average_relationship_score"] = totalScore / float64(len(networks))
	}

	return trends, nil
}

// Helper types and functions
type ContactMetrics struct {
	Contact          entities.Contact
	MessagesSent     int
	MessagesReceived int
	ResponseTimes    []float64
	InitiatedChats   int
	LastInteraction  time.Time
	InteractionDays  map[string]bool
	MessageLengths   []int
	EmojiCount       int
	TotalMessages    int
}

func (s *service) analyzeMessagesForSocialNetwork(messages []entities.Message, contactMetrics map[uuid.UUID]*ContactMetrics) {
	for i, message := range messages {
		if message.ContactID == nil {
			continue
		}

		contactID := *message.ContactID
		metrics, exists := contactMetrics[contactID]
		if !exists {
			continue
		}

		metrics.TotalMessages++

		// Track interaction days
		dayKey := message.Timestamp.Format("2006-01-02")
		metrics.InteractionDays[dayKey] = true

		// Update last interaction
		if message.Timestamp.After(metrics.LastInteraction) {
			metrics.LastInteraction = message.Timestamp
		}

		// Message length
		metrics.MessageLengths = append(metrics.MessageLengths, message.CharCount)

		// Emoji count
		if message.HasEmoji {
			metrics.EmojiCount += message.EmojiCount
		}

		// Response time analysis
		if i > 0 {
			prevMessage := messages[i-1]
			if prevMessage.ContactID != nil && *prevMessage.ContactID != contactID {
				// Different sender, calculate response time
				responseTime := message.Timestamp.Sub(prevMessage.Timestamp).Minutes()
				if responseTime > 0 && responseTime < 1440 { // Less than 24 hours
					metrics.ResponseTimes = append(metrics.ResponseTimes, responseTime)
				}
			}
		}

		// Count sent vs received
		if message.IsFromUser {
			metrics.MessagesSent++
		} else {
			metrics.MessagesReceived++
		}
	}
}

func (s *service) calculateRelationshipNode(metrics *ContactMetrics) dtos.SocialNetworkNode {
	node := dtos.SocialNetworkNode{
		ContactID:        metrics.Contact.ID,
		ContactName:      metrics.Contact.Name,
		Platform:         metrics.Contact.Platform,
		MessageExchanges: metrics.TotalMessages,
		LastInteraction:  metrics.LastInteraction,
		InteractionDays:  len(metrics.InteractionDays),
	}

	// Calculate response rate
	if metrics.MessagesReceived > 0 {
		node.ResponseRate = float64(metrics.MessagesSent) / float64(metrics.MessagesReceived) * 100
	}

	// Calculate initiation rate (simplified)
	node.InitiationRate = float64(metrics.InitiatedChats) / float64(metrics.TotalMessages) * 100

	// Calculate average response time
	if len(metrics.ResponseTimes) > 0 {
		total := 0.0
		for _, rt := range metrics.ResponseTimes {
			total += rt
		}
		node.AvgResponseTime = total / float64(len(metrics.ResponseTimes))
	}

	// Calculate average message length
	if len(metrics.MessageLengths) > 0 {
		total := 0
		for _, length := range metrics.MessageLengths {
			total += length
		}
		node.AvgMessageLength = float64(total) / float64(len(metrics.MessageLengths))
	}

	// Calculate emoji usage rate
	if metrics.TotalMessages > 0 {
		node.EmojiUsageRate = float64(metrics.EmojiCount) / float64(metrics.TotalMessages) * 100
	}

	// Calculate relationship score
	node.RelationshipScore = s.calculateRelationshipScore(metrics, &node)

	// Determine trend direction (simplified)
	node.TrendDirection = s.calculateTrendDirection(metrics)

	return node
}

func (s *service) calculateRelationshipScore(metrics *ContactMetrics, node *dtos.SocialNetworkNode) float64 {
	score := 0.0

	// Frequency score (30% weight)
	frequencyScore := math.Min(100, float64(metrics.TotalMessages)/50*100)
	score += frequencyScore * 0.3

	// Recency score (25% weight)
	daysSinceLastInteraction := time.Since(metrics.LastInteraction).Hours() / 24
	recencyScore := math.Max(0, 100-daysSinceLastInteraction*2)
	score += recencyScore * 0.25

	// Consistency score (20% weight) - based on interaction days
	consistencyScore := math.Min(100, float64(len(metrics.InteractionDays))/30*100)
	score += consistencyScore * 0.2

	// Response balance score (15% weight)
	balanceScore := 100.0
	if metrics.MessagesSent > 0 && metrics.MessagesReceived > 0 {
		ratio := float64(metrics.MessagesSent) / float64(metrics.MessagesReceived)
		if ratio > 1 {
			ratio = 1 / ratio
		}
		balanceScore = ratio * 100
	}
	score += balanceScore * 0.15

	// Engagement score (10% weight) - based on message length and emoji usage
	engagementScore := math.Min(100, node.AvgMessageLength/50*100)
	if node.EmojiUsageRate > 0 {
		engagementScore += math.Min(20, node.EmojiUsageRate)
	}
	score += math.Min(100, engagementScore) * 0.1

	return math.Min(100, score)
}

func (s *service) calculateTrendDirection(metrics *ContactMetrics) string {
	// Simple trend calculation based on recent activity
	now := time.Now()
	recentDays := 30
	recentStart := now.AddDate(0, 0, -recentDays)

	recentInteractions := 0
	for dayStr := range metrics.InteractionDays {
		if day, err := time.Parse("2006-01-02", dayStr); err == nil {
			if day.After(recentStart) {
				recentInteractions++
			}
		}
	}

	// Compare with overall average
	totalDays := int(time.Since(metrics.LastInteraction).Hours() / 24)
	if totalDays == 0 {
		return "stable"
	}

	overallAvg := float64(len(metrics.InteractionDays)) / float64(totalDays) * 30
	recentAvg := float64(recentInteractions)

	if recentAvg > overallAvg*1.2 {
		return "increasing"
	} else if recentAvg < overallAvg*0.8 {
		return "decreasing"
	}

	return "stable"
}
