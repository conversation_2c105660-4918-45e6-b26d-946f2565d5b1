package analytics

import (
	"encoding/json"
	"fmt"
	"math"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/go-base-project-structure/pkg/domains/chat"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	AnalyzeChat(userID, chatID uuid.UUID) (*entities.ChatAnalysis, error)
	GenerateWrappedSummary(userID uuid.UUID, year int) (*entities.ChatAnalysis, error)
	GetUserAnalytics(userID uuid.UUID, analysisType entities.AnalysisType) ([]entities.ChatAnalysis, error)
	GetChatAnalytics(chatID uuid.UUID) (*entities.ChatAnalysis, error)
	AnalyzeEmojis(userID, chatID uuid.UUID) ([]entities.EmojiUsage, error)
	AnalyzeWords(userID, chatID uuid.UUID) ([]entities.WordUsage, error)
	AnalyzeSocialNetwork(userID uuid.UUID) ([]entities.SocialNetwork, error)
	GetChatInsights(chatID uuid.UUID) (map[string]interface{}, error)
}

type service struct {
	repo     Repository
	chatRepo chat.Repository
}

func NewService(repo Repository, chatRepo chat.Repository) Service {
	return &service{
		repo:     repo,
		chatRepo: chatRepo,
	}
}

func (s *service) AnalyzeChat(userID, chatID uuid.UUID) (*entities.ChatAnalysis, error) {
	// Get chat and messages
	chatEntity, err := s.chatRepo.GetChatByID(chatID)
	if err != nil {
		return nil, err
	}

	if chatEntity.UserID != userID {
		return nil, fmt.Errorf("unauthorized access to chat")
	}

	messages, err := s.chatRepo.GetMessagesByChatID(chatID, 10000, 0) // Get all messages
	if err != nil {
		return nil, err
	}

	if len(messages) == 0 {
		return nil, fmt.Errorf("no messages found for analysis")
	}

	// Create analysis
	analysis := &entities.ChatAnalysis{
		UserID:       userID,
		ChatID:       chatID,
		AnalysisType: entities.DailyAnalysis,
		StartDate:    messages[len(messages)-1].Timestamp, // Oldest message
		EndDate:      messages[0].Timestamp,               // Newest message
		TopContacts:  "[]",                                // Initialize with empty JSON array
	}

	// Basic statistics
	analysis.TotalMessages = len(messages)
	analysis.MessagesSent = s.countMessagesByUser(messages, true)
	analysis.MessagesReceived = analysis.TotalMessages - analysis.MessagesSent

	// Word and character analysis
	totalWords, totalChars := s.analyzeTextMetrics(messages)
	analysis.TotalWords = totalWords
	analysis.TotalCharacters = totalChars
	if analysis.TotalMessages > 0 {
		analysis.AvgWordsPerMessage = float64(totalWords) / float64(analysis.TotalMessages)
	}

	// Time analysis
	analysis.MostActiveHour = s.findMostActiveHour(messages)
	analysis.MostActiveDay = s.findMostActiveDay(messages)
	analysis.AvgResponseTime = s.calculateAverageResponseTime(messages)

	// Sentiment analysis (basic implementation)
	positive, negative, neutral := s.analyzeSentiment(messages)
	analysis.PositiveMessages = positive
	analysis.NegativeMessages = negative
	analysis.NeutralMessages = neutral
	if analysis.TotalMessages > 0 {
		analysis.OverallSentiment = float64(positive-negative) / float64(analysis.TotalMessages)
	}

	// Emoji analysis
	emojiStats := s.analyzeEmojis(messages)
	analysis.TotalEmojis = emojiStats["total"].(int)
	analysis.UniqueEmojis = emojiStats["unique"].(int)
	topEmojisJSON, _ := json.Marshal(emojiStats["top"])
	analysis.TopEmojis = string(topEmojisJSON)

	// Word analysis
	wordStats := s.analyzeWords(messages)
	analysis.UniqueWords = wordStats["unique"].(int)
	topWordsJSON, _ := json.Marshal(wordStats["top"])
	analysis.TopWords = string(topWordsJSON)

	// Conversation patterns
	sessions := s.analyzeConversationSessions(messages)
	if len(sessions) > 0 {
		analysis.LongestConversation = s.findLongestSession(sessions)
		analysis.AvgConversationLength = s.calculateAverageSessionLength(sessions)
	}

	// Generate insights
	analysis.Insights = s.generateInsights(analysis, messages)
	analysis.Summary = s.generateSummary(analysis)

	// Save analysis
	if err := s.repo.CreateAnalysis(analysis); err != nil {
		return nil, err
	}

	// Mark chat as analyzed
	chatEntity.IsAnalyzed = true
	chatEntity.AnalyzedAt = &time.Time{}
	*chatEntity.AnalyzedAt = time.Now()
	s.chatRepo.UpdateChat(chatEntity)

	return analysis, nil
}

func (s *service) GenerateWrappedSummary(userID uuid.UUID, year int) (*entities.ChatAnalysis, error) {
	startDate := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(year, 12, 31, 23, 59, 59, 0, time.UTC)

	// Get all user chats
	chats, err := s.chatRepo.GetChatsByUserID(userID)
	if err != nil {
		return nil, err
	}

	analysis := &entities.ChatAnalysis{
		UserID:       userID,
		AnalysisType: entities.WrappedAnalysis,
		StartDate:    startDate,
		EndDate:      endDate,
	}

	totalMessages := 0
	totalWords := 0
	totalChars := 0
	allMessages := []entities.Message{}

	// Aggregate data from all chats
	for _, chat := range chats {
		messages, err := s.chatRepo.GetMessagesByDateRange(chat.ID, startDate, endDate)
		if err != nil {
			continue
		}

		totalMessages += len(messages)
		allMessages = append(allMessages, messages...)

		words, chars := s.analyzeTextMetrics(messages)
		totalWords += words
		totalChars += chars
	}

	analysis.TotalMessages = totalMessages
	analysis.TotalWords = totalWords
	analysis.TotalCharacters = totalChars

	if totalMessages > 0 {
		analysis.AvgWordsPerMessage = float64(totalWords) / float64(totalMessages)

		// Analyze aggregated data
		analysis.MostActiveHour = s.findMostActiveHour(allMessages)
		analysis.MostActiveDay = s.findMostActiveDay(allMessages)

		// Sentiment analysis
		positive, negative, neutral := s.analyzeSentiment(allMessages)
		analysis.PositiveMessages = positive
		analysis.NegativeMessages = negative
		analysis.NeutralMessages = neutral
		analysis.OverallSentiment = float64(positive-negative) / float64(totalMessages)

		// Emoji and word analysis
		emojiStats := s.analyzeEmojis(allMessages)
		analysis.TotalEmojis = emojiStats["total"].(int)
		analysis.UniqueEmojis = emojiStats["unique"].(int)
		topEmojisJSON, _ := json.Marshal(emojiStats["top"])
		analysis.TopEmojis = string(topEmojisJSON)

		wordStats := s.analyzeWords(allMessages)
		analysis.UniqueWords = wordStats["unique"].(int)
		topWordsJSON, _ := json.Marshal(wordStats["top"])
		analysis.TopWords = string(topWordsJSON)
	}

	// Generate wrapped insights
	analysis.Insights = s.generateWrappedInsights(analysis, len(chats))
	analysis.Summary = fmt.Sprintf("Your %d Wrapped: %d messages across %d chats", year, totalMessages, len(chats))

	// Save analysis
	if err := s.repo.CreateAnalysis(analysis); err != nil {
		return nil, err
	}

	return analysis, nil
}

func (s *service) GetUserAnalytics(userID uuid.UUID, analysisType entities.AnalysisType) ([]entities.ChatAnalysis, error) {
	return s.repo.GetAnalysesByUserID(userID, analysisType)
}

func (s *service) GetChatAnalytics(chatID uuid.UUID) (*entities.ChatAnalysis, error) {
	analyses, err := s.repo.GetAnalysesByChatID(chatID)
	if err != nil {
		return nil, err
	}

	if len(analyses) == 0 {
		return nil, fmt.Errorf("no analysis found for chat")
	}

	return &analyses[0], nil // Return most recent analysis
}

func (s *service) AnalyzeEmojis(userID, chatID uuid.UUID) ([]entities.EmojiUsage, error) {
	messages, err := s.chatRepo.GetMessagesByChatID(chatID, 10000, 0)
	if err != nil {
		return nil, err
	}

	emojiCounts := make(map[string]int)
	emojiRegex := regexp.MustCompile(`[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]`)

	for _, message := range messages {
		emojis := emojiRegex.FindAllString(message.Content, -1)
		for _, emoji := range emojis {
			emojiCounts[emoji]++
		}
	}

	var emojiUsages []entities.EmojiUsage
	for emoji, count := range emojiCounts {
		usage := entities.EmojiUsage{
			UserID:   userID,
			ChatID:   &chatID,
			Emoji:    emoji,
			Count:    count,
			LastUsed: time.Now(),
		}
		emojiUsages = append(emojiUsages, usage)
		s.repo.CreateEmojiUsage(&usage)
	}

	// Sort by count
	sort.Slice(emojiUsages, func(i, j int) bool {
		return emojiUsages[i].Count > emojiUsages[j].Count
	})

	return emojiUsages, nil
}

func (s *service) AnalyzeWords(userID, chatID uuid.UUID) ([]entities.WordUsage, error) {
	messages, err := s.chatRepo.GetMessagesByChatID(chatID, 10000, 0)
	if err != nil {
		return nil, err
	}

	wordCounts := make(map[string]int)
	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true, "but": true,
		"in": true, "on": true, "at": true, "to": true, "for": true, "of": true,
		"with": true, "by": true, "is": true, "are": true, "was": true, "were": true,
		"be": true, "been": true, "have": true, "has": true, "had": true, "do": true,
		"does": true, "did": true, "will": true, "would": true, "could": true, "should": true,
	}

	for _, message := range messages {
		words := strings.Fields(strings.ToLower(message.Content))
		for _, word := range words {
			// Clean word
			word = regexp.MustCompile(`[^\w]`).ReplaceAllString(word, "")
			if len(word) > 2 && !stopWords[word] {
				wordCounts[word]++
			}
		}
	}

	var wordUsages []entities.WordUsage
	for word, count := range wordCounts {
		if count > 1 { // Only include words used more than once
			usage := entities.WordUsage{
				UserID:   userID,
				ChatID:   &chatID,
				Word:     word,
				Count:    count,
				LastUsed: time.Now(),
			}
			wordUsages = append(wordUsages, usage)
			s.repo.CreateWordUsage(&usage)
		}
	}

	// Sort by count
	sort.Slice(wordUsages, func(i, j int) bool {
		return wordUsages[i].Count > wordUsages[j].Count
	})

	return wordUsages, nil
}

func (s *service) AnalyzeSocialNetwork(userID uuid.UUID) ([]entities.SocialNetwork, error) {
	chats, err := s.chatRepo.GetChatsByUserID(userID)
	if err != nil {
		return nil, err
	}

	contactStats := make(map[uuid.UUID]*entities.SocialNetwork)

	for _, chat := range chats {
		messages, err := s.chatRepo.GetMessagesByChatID(chat.ID, 10000, 0)
		if err != nil {
			continue
		}

		for _, message := range messages {
			if message.ContactID == nil {
				continue
			}

			contactID := *message.ContactID
			if _, exists := contactStats[contactID]; !exists {
				contactStats[contactID] = &entities.SocialNetwork{
					UserID:    userID,
					ContactID: contactID,
				}
			}

			stats := contactStats[contactID]
			stats.MessageExchanges++
			stats.LastInteraction = message.Timestamp

			if message.IsFromUser {
				// User sent message
			} else {
				// Contact sent message
			}
		}
	}

	// Calculate relationship scores
	var networks []entities.SocialNetwork
	for _, stats := range contactStats {
		// Simple relationship score calculation
		daysSinceLastInteraction := time.Since(stats.LastInteraction).Hours() / 24
		recencyScore := math.Max(0, 100-daysSinceLastInteraction)
		frequencyScore := math.Min(100, float64(stats.MessageExchanges)/10)

		stats.RelationshipScore = (recencyScore + frequencyScore) / 2

		if stats.RelationshipScore > 10 { // Only include meaningful relationships
			s.repo.CreateSocialNetwork(stats)
			networks = append(networks, *stats)
		}
	}

	// Sort by relationship score
	sort.Slice(networks, func(i, j int) bool {
		return networks[i].RelationshipScore > networks[j].RelationshipScore
	})

	return networks, nil
}

func (s *service) GetChatInsights(chatID uuid.UUID) (map[string]interface{}, error) {
	insights := make(map[string]interface{})

	// Get basic stats
	hourlyStats, err := s.repo.GetMessageCountByHour(chatID)
	if err == nil {
		insights["hourly_distribution"] = hourlyStats
	}

	dailyStats, err := s.repo.GetMessageCountByDay(chatID)
	if err == nil {
		insights["daily_distribution"] = dailyStats
	}

	contactStats, err := s.repo.GetMessageCountByContact(chatID)
	if err == nil {
		insights["contact_distribution"] = contactStats
	}

	avgResponseTime, err := s.repo.GetAverageResponseTime(chatID)
	if err == nil {
		insights["avg_response_time"] = avgResponseTime
	}

	sentimentDist, err := s.repo.GetSentimentDistribution(chatID)
	if err == nil {
		insights["sentiment_distribution"] = sentimentDist
	}

	return insights, nil
}

// Helper functions
func (s *service) countMessagesByUser(messages []entities.Message, isFromUser bool) int {
	count := 0
	for _, msg := range messages {
		if msg.IsFromUser == isFromUser {
			count++
		}
	}
	return count
}

func (s *service) analyzeTextMetrics(messages []entities.Message) (int, int) {
	totalWords := 0
	totalChars := 0
	for _, msg := range messages {
		totalWords += msg.WordCount
		totalChars += msg.CharCount
	}
	return totalWords, totalChars
}

func (s *service) findMostActiveHour(messages []entities.Message) int {
	hourCounts := make(map[int]int)
	for _, msg := range messages {
		hour := msg.Timestamp.Hour()
		hourCounts[hour]++
	}

	maxCount := 0
	mostActiveHour := 0
	for hour, count := range hourCounts {
		if count > maxCount {
			maxCount = count
			mostActiveHour = hour
		}
	}
	return mostActiveHour
}

func (s *service) findMostActiveDay(messages []entities.Message) string {
	dayCounts := make(map[string]int)
	days := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}

	for _, msg := range messages {
		day := days[msg.Timestamp.Weekday()]
		dayCounts[day]++
	}

	maxCount := 0
	mostActiveDay := ""
	for day, count := range dayCounts {
		if count > maxCount {
			maxCount = count
			mostActiveDay = day
		}
	}
	return mostActiveDay
}

func (s *service) calculateAverageResponseTime(messages []entities.Message) float64 {
	if len(messages) < 2 {
		return 0
	}

	totalResponseTime := int64(0)
	responseCount := 0

	for i := 1; i < len(messages); i++ {
		if messages[i].IsFromUser != messages[i-1].IsFromUser {
			responseTime := messages[i].Timestamp.Sub(messages[i-1].Timestamp).Milliseconds()
			if responseTime > 0 && responseTime < 24*60*60*1000 { // Less than 24 hours
				totalResponseTime += responseTime
				responseCount++
			}
		}
	}

	if responseCount == 0 {
		return 0
	}

	return float64(totalResponseTime) / float64(responseCount)
}

func (s *service) analyzeSentiment(messages []entities.Message) (int, int, int) {
	positive := 0
	negative := 0
	neutral := 0

	// Simple sentiment analysis based on keywords
	positiveWords := []string{"good", "great", "awesome", "love", "happy", "excellent", "amazing", "wonderful"}
	negativeWords := []string{"bad", "terrible", "hate", "sad", "angry", "awful", "horrible", "worst"}

	for _, msg := range messages {
		content := strings.ToLower(msg.Content)
		hasPositive := false
		hasNegative := false

		for _, word := range positiveWords {
			if strings.Contains(content, word) {
				hasPositive = true
				break
			}
		}

		for _, word := range negativeWords {
			if strings.Contains(content, word) {
				hasNegative = true
				break
			}
		}

		if hasPositive && !hasNegative {
			positive++
		} else if hasNegative && !hasPositive {
			negative++
		} else {
			neutral++
		}
	}

	return positive, negative, neutral
}

func (s *service) analyzeEmojis(messages []entities.Message) map[string]interface{} {
	emojiCounts := make(map[string]int)
	emojiRegex := regexp.MustCompile(`[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]`)

	totalEmojis := 0
	for _, message := range messages {
		emojis := emojiRegex.FindAllString(message.Content, -1)
		totalEmojis += len(emojis)
		for _, emoji := range emojis {
			emojiCounts[emoji]++
		}
	}

	// Get top 10 emojis
	type emojiCount struct {
		Emoji string `json:"emoji"`
		Count int    `json:"count"`
	}

	var topEmojis []emojiCount
	for emoji, count := range emojiCounts {
		topEmojis = append(topEmojis, emojiCount{Emoji: emoji, Count: count})
	}

	sort.Slice(topEmojis, func(i, j int) bool {
		return topEmojis[i].Count > topEmojis[j].Count
	})

	if len(topEmojis) > 10 {
		topEmojis = topEmojis[:10]
	}

	return map[string]interface{}{
		"total":  totalEmojis,
		"unique": len(emojiCounts),
		"top":    topEmojis,
	}
}

func (s *service) analyzeWords(messages []entities.Message) map[string]interface{} {
	wordCounts := make(map[string]int)
	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true, "but": true,
		"in": true, "on": true, "at": true, "to": true, "for": true, "of": true,
	}

	for _, message := range messages {
		words := strings.Fields(strings.ToLower(message.Content))
		for _, word := range words {
			word = regexp.MustCompile(`[^\w]`).ReplaceAllString(word, "")
			if len(word) > 2 && !stopWords[word] {
				wordCounts[word]++
			}
		}
	}

	type wordCount struct {
		Word  string `json:"word"`
		Count int    `json:"count"`
	}

	var topWords []wordCount
	for word, count := range wordCounts {
		if count > 1 {
			topWords = append(topWords, wordCount{Word: word, Count: count})
		}
	}

	sort.Slice(topWords, func(i, j int) bool {
		return topWords[i].Count > topWords[j].Count
	})

	if len(topWords) > 20 {
		topWords = topWords[:20]
	}

	return map[string]interface{}{
		"unique": len(wordCounts),
		"top":    topWords,
	}
}

func (s *service) analyzeConversationSessions(messages []entities.Message) []entities.ConversationSession {
	if len(messages) == 0 {
		return nil
	}

	var sessions []entities.ConversationSession
	sessionGap := 30 * time.Minute // 30 minutes gap defines new session

	currentSession := entities.ConversationSession{
		StartTime:    messages[0].Timestamp,
		MessageCount: 1,
	}

	for i := 1; i < len(messages); i++ {
		timeDiff := messages[i].Timestamp.Sub(messages[i-1].Timestamp)

		if timeDiff > sessionGap {
			// End current session
			currentSession.EndTime = messages[i-1].Timestamp
			currentSession.Duration = int64(currentSession.EndTime.Sub(currentSession.StartTime).Seconds())
			sessions = append(sessions, currentSession)

			// Start new session
			currentSession = entities.ConversationSession{
				StartTime:    messages[i].Timestamp,
				MessageCount: 1,
			}
		} else {
			currentSession.MessageCount++
		}
	}

	// Close last session
	currentSession.EndTime = messages[len(messages)-1].Timestamp
	currentSession.Duration = int64(currentSession.EndTime.Sub(currentSession.StartTime).Seconds())
	sessions = append(sessions, currentSession)

	return sessions
}

func (s *service) findLongestSession(sessions []entities.ConversationSession) int {
	maxMessages := 0
	for _, session := range sessions {
		if session.MessageCount > maxMessages {
			maxMessages = session.MessageCount
		}
	}
	return maxMessages
}

func (s *service) calculateAverageSessionLength(sessions []entities.ConversationSession) float64 {
	if len(sessions) == 0 {
		return 0
	}

	totalMessages := 0
	for _, session := range sessions {
		totalMessages += session.MessageCount
	}

	return float64(totalMessages) / float64(len(sessions))
}

func (s *service) generateInsights(analysis *entities.ChatAnalysis, messages []entities.Message) string {
	insights := []string{}

	if analysis.TotalMessages > 100 {
		insights = append(insights, fmt.Sprintf("Very active chat with %d messages", analysis.TotalMessages))
	}

	if analysis.OverallSentiment > 0.1 {
		insights = append(insights, "Generally positive conversation tone")
	} else if analysis.OverallSentiment < -0.1 {
		insights = append(insights, "Generally negative conversation tone")
	}

	if analysis.MostActiveHour >= 9 && analysis.MostActiveHour <= 17 {
		insights = append(insights, "Most active during work hours")
	} else if analysis.MostActiveHour >= 22 || analysis.MostActiveHour <= 6 {
		insights = append(insights, "Most active during late night/early morning")
	}

	if analysis.AvgResponseTime < 300000 { // Less than 5 minutes
		insights = append(insights, "Very quick response times")
	}

	return strings.Join(insights, ". ")
}

func (s *service) generateSummary(analysis *entities.ChatAnalysis) string {
	return fmt.Sprintf("Chat analysis: %d messages, %d words, %.1f%% positive sentiment, most active on %s at %d:00",
		analysis.TotalMessages, analysis.TotalWords, analysis.OverallSentiment*100, analysis.MostActiveDay, analysis.MostActiveHour)
}

func (s *service) generateWrappedInsights(analysis *entities.ChatAnalysis, chatCount int) string {
	insights := []string{}

	insights = append(insights, fmt.Sprintf("You sent %d messages across %d chats this year", analysis.TotalMessages, chatCount))

	if analysis.TotalWords > 10000 {
		insights = append(insights, fmt.Sprintf("You wrote %d words - that's like a short book!", analysis.TotalWords))
	}

	if analysis.OverallSentiment > 0.1 {
		insights = append(insights, "Your conversations were mostly positive this year")
	}

	insights = append(insights, fmt.Sprintf("You were most active on %s", analysis.MostActiveDay))

	return strings.Join(insights, ". ")
}
