package analytics

import (
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	// Analysis operations
	CreateAnalysis(analysis *entities.ChatAnalysis) error
	GetAnalysisByID(id uuid.UUID) (*entities.ChatAnalysis, error)
	GetAnalysesByUserID(userID uuid.UUID, analysisType entities.AnalysisType) ([]entities.ChatAnalysis, error)
	GetAnalysesByChatID(chatID uuid.UUID) ([]entities.ChatAnalysis, error)
	UpdateAnalysis(analysis *entities.ChatAnalysis) error

	// Emoji usage
	CreateEmojiUsage(usage *entities.EmojiUsage) error
	GetEmojiUsageByUser(userID uuid.UUID, limit int) ([]entities.EmojiUsage, error)
	GetEmojiUsageByChat(chatID uuid.UUID, limit int) ([]entities.EmojiUsage, error)
	UpdateEmojiUsage(usage *entities.EmojiUsage) error

	// Word usage
	CreateWordUsage(usage *entities.WordUsage) error
	GetWordUsageByUser(userID uuid.UUID, limit int) ([]entities.WordUsage, error)
	GetWordUsageByChat(chatID uuid.UUID, limit int) ([]entities.WordUsage, error)
	UpdateWordUsage(usage *entities.WordUsage) error

	// Conversation sessions
	CreateConversationSession(session *entities.ConversationSession) error
	GetConversationSessionsByChat(chatID uuid.UUID) ([]entities.ConversationSession, error)

	// Social network
	CreateSocialNetwork(network *entities.SocialNetwork) error
	GetSocialNetworkByUser(userID uuid.UUID) ([]entities.SocialNetwork, error)
	UpdateSocialNetwork(network *entities.SocialNetwork) error

	// Analytics queries
	GetMessageCountByHour(chatID uuid.UUID) (map[int]int64, error)
	GetMessageCountByDay(chatID uuid.UUID) (map[string]int64, error)
	GetMessageCountByContact(chatID uuid.UUID) (map[string]int64, error)
	GetAverageResponseTime(chatID uuid.UUID) (float64, error)
	GetSentimentDistribution(chatID uuid.UUID) (map[string]int64, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateAnalysis(analysis *entities.ChatAnalysis) error {
	return r.db.Create(analysis).Error
}

func (r *repository) GetAnalysisByID(id uuid.UUID) (*entities.ChatAnalysis, error) {
	var analysis entities.ChatAnalysis
	err := r.db.Preload("User").Preload("Chat").First(&analysis, "id = ?", id).Error
	return &analysis, err
}

func (r *repository) GetAnalysesByUserID(userID uuid.UUID, analysisType entities.AnalysisType) ([]entities.ChatAnalysis, error) {
	var analyses []entities.ChatAnalysis
	query := r.db.Where("user_id = ?", userID)
	if analysisType != "" {
		query = query.Where("analysis_type = ?", analysisType)
	}
	err := query.Order("created_at DESC").Find(&analyses).Error
	return analyses, err
}

func (r *repository) GetAnalysesByChatID(chatID uuid.UUID) ([]entities.ChatAnalysis, error) {
	var analyses []entities.ChatAnalysis
	err := r.db.Where("chat_id = ?", chatID).Order("created_at DESC").Find(&analyses).Error
	return analyses, err
}

func (r *repository) UpdateAnalysis(analysis *entities.ChatAnalysis) error {
	return r.db.Save(analysis).Error
}

func (r *repository) CreateEmojiUsage(usage *entities.EmojiUsage) error {
	return r.db.Create(usage).Error
}

func (r *repository) GetEmojiUsageByUser(userID uuid.UUID, limit int) ([]entities.EmojiUsage, error) {
	var usages []entities.EmojiUsage
	err := r.db.Where("user_id = ?", userID).Order("count DESC").Limit(limit).Find(&usages).Error
	return usages, err
}

func (r *repository) GetEmojiUsageByChat(chatID uuid.UUID, limit int) ([]entities.EmojiUsage, error) {
	var usages []entities.EmojiUsage
	err := r.db.Where("chat_id = ?", chatID).Order("count DESC").Limit(limit).Find(&usages).Error
	return usages, err
}

func (r *repository) UpdateEmojiUsage(usage *entities.EmojiUsage) error {
	return r.db.Save(usage).Error
}

func (r *repository) CreateWordUsage(usage *entities.WordUsage) error {
	return r.db.Create(usage).Error
}

func (r *repository) GetWordUsageByUser(userID uuid.UUID, limit int) ([]entities.WordUsage, error) {
	var usages []entities.WordUsage
	err := r.db.Where("user_id = ?", userID).Order("count DESC").Limit(limit).Find(&usages).Error
	return usages, err
}

func (r *repository) GetWordUsageByChat(chatID uuid.UUID, limit int) ([]entities.WordUsage, error) {
	var usages []entities.WordUsage
	err := r.db.Where("chat_id = ?", chatID).Order("count DESC").Limit(limit).Find(&usages).Error
	return usages, err
}

func (r *repository) UpdateWordUsage(usage *entities.WordUsage) error {
	return r.db.Save(usage).Error
}

func (r *repository) CreateConversationSession(session *entities.ConversationSession) error {
	return r.db.Create(session).Error
}

func (r *repository) GetConversationSessionsByChat(chatID uuid.UUID) ([]entities.ConversationSession, error) {
	var sessions []entities.ConversationSession
	err := r.db.Where("chat_id = ?", chatID).Order("start_time DESC").Find(&sessions).Error
	return sessions, err
}

func (r *repository) CreateSocialNetwork(network *entities.SocialNetwork) error {
	return r.db.Create(network).Error
}

func (r *repository) GetSocialNetworkByUser(userID uuid.UUID) ([]entities.SocialNetwork, error) {
	var networks []entities.SocialNetwork
	err := r.db.Preload("Contact").Where("user_id = ?", userID).
		Order("relationship_score DESC").Find(&networks).Error
	return networks, err
}

func (r *repository) UpdateSocialNetwork(network *entities.SocialNetwork) error {
	return r.db.Save(network).Error
}

func (r *repository) GetMessageCountByHour(chatID uuid.UUID) (map[int]int64, error) {
	var results []struct {
		Hour  int   `json:"hour"`
		Count int64 `json:"count"`
	}

	err := r.db.Table("messages").
		Select("EXTRACT(hour FROM timestamp) as hour, COUNT(*) as count").
		Where("chat_id = ?", chatID).
		Group("hour").
		Order("hour").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	hourMap := make(map[int]int64)
	for _, result := range results {
		hourMap[result.Hour] = result.Count
	}

	return hourMap, nil
}

func (r *repository) GetMessageCountByDay(chatID uuid.UUID) (map[string]int64, error) {
	var results []struct {
		Day   string `json:"day"`
		Count int64  `json:"count"`
	}

	err := r.db.Table("messages").
		Select("TO_CHAR(timestamp, 'Day') as day, COUNT(*) as count").
		Where("chat_id = ?", chatID).
		Group("day").
		Order("count DESC").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	dayMap := make(map[string]int64)
	for _, result := range results {
		dayMap[result.Day] = result.Count
	}

	return dayMap, nil
}

func (r *repository) GetMessageCountByContact(chatID uuid.UUID) (map[string]int64, error) {
	var results []struct {
		ContactName string `json:"contact_name"`
		Count       int64  `json:"count"`
	}

	err := r.db.Table("messages").
		Select("contacts.name as contact_name, COUNT(*) as count").
		Joins("LEFT JOIN contacts ON messages.contact_id = contacts.id").
		Where("messages.chat_id = ?", chatID).
		Group("contacts.name").
		Order("count DESC").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	contactMap := make(map[string]int64)
	for _, result := range results {
		contactMap[result.ContactName] = result.Count
	}

	return contactMap, nil
}

func (r *repository) GetAverageResponseTime(chatID uuid.UUID) (float64, error) {
	var avgTime float64
	err := r.db.Table("messages").
		Select("AVG(response_time_ms)").
		Where("chat_id = ? AND response_time_ms IS NOT NULL", chatID).
		Scan(&avgTime).Error

	return avgTime, err
}

func (r *repository) GetSentimentDistribution(chatID uuid.UUID) (map[string]int64, error) {
	var results []struct {
		SentimentLabel string `json:"sentiment_label"`
		Count          int64  `json:"count"`
	}

	err := r.db.Table("messages").
		Select("sentiment_label, COUNT(*) as count").
		Where("chat_id = ? AND sentiment_label IS NOT NULL", chatID).
		Group("sentiment_label").
		Order("count DESC").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	sentimentMap := make(map[string]int64)
	for _, result := range results {
		sentimentMap[result.SentimentLabel] = result.Count
	}

	return sentimentMap, nil
}
