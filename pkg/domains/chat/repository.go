package chat

import (
	"time"

	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	// Chat operations
	CreateChat(chat *entities.Chat) error
	GetChatByID(id uuid.UUID) (*entities.Chat, error)
	GetChatsByUserID(userID uuid.UUID) ([]entities.Chat, error)
	UpdateChat(chat *entities.Chat) error
	DeleteChat(id uuid.UUID) error

	// Message operations
	CreateMessage(message *entities.Message) error
	CreateMessages(messages []entities.Message) error
	GetMessagesByChatID(chatID uuid.UUID, limit, offset int) ([]entities.Message, error)
	GetMessagesByDateRange(chatID uuid.UUID, start, end time.Time) ([]entities.Message, error)
	GetMessageCount(chatID uuid.UUID) (int64, error)

	// Contact operations
	CreateContact(contact *entities.Contact) error
	GetContactByNameAndPlatform(userID uuid.UUID, name string, platform entities.ChatPlatform) (*entities.Contact, error)
	GetContactsByUserID(userID uuid.UUID) ([]entities.Contact, error)
	GetContactByID(contactID uuid.UUID) (*entities.Contact, error)
	UpdateContact(contact *entities.Contact) error

	// Chat participants
	AddParticipantToChat(chatID, contactID uuid.UUID) error
	GetChatParticipants(chatID uuid.UUID) ([]entities.Contact, error)

	// Analysis data
	GetChatAnalysisByID(id uuid.UUID) (*entities.ChatAnalysis, error)
	CreateChatAnalysis(analysis *entities.ChatAnalysis) error
	GetAnalysesByUserID(userID uuid.UUID, analysisType entities.AnalysisType) ([]entities.ChatAnalysis, error)

	// Statistics
	GetUserMessageStats(userID uuid.UUID) (map[string]interface{}, error)
	GetChatMessageStats(chatID uuid.UUID) (map[string]interface{}, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateChat(chat *entities.Chat) error {
	return r.db.Create(chat).Error
}

func (r *repository) GetChatByID(id uuid.UUID) (*entities.Chat, error) {
	var chat entities.Chat
	err := r.db.First(&chat, "id = ?", id).Error
	return &chat, err
}

func (r *repository) GetChatsByUserID(userID uuid.UUID) ([]entities.Chat, error) {
	var chats []entities.Chat
	err := r.db.Where("user_id = ?", userID).Find(&chats).Error
	return chats, err
}

func (r *repository) UpdateChat(chat *entities.Chat) error {
	return r.db.Save(chat).Error
}

func (r *repository) DeleteChat(id uuid.UUID) error {
	return r.db.Delete(&entities.Chat{}, "id = ?", id).Error
}

func (r *repository) CreateMessage(message *entities.Message) error {
	return r.db.Create(message).Error
}

func (r *repository) CreateMessages(messages []entities.Message) error {
	return r.db.CreateInBatches(messages, 1000).Error
}

func (r *repository) GetMessagesByChatID(chatID uuid.UUID, limit, offset int) ([]entities.Message, error) {
	var messages []entities.Message
	err := r.db.Where("chat_id = ?", chatID).
		Order("timestamp DESC").Limit(limit).Offset(offset).Find(&messages).Error
	return messages, err
}

func (r *repository) GetMessagesByDateRange(chatID uuid.UUID, start, end time.Time) ([]entities.Message, error) {
	var messages []entities.Message
	err := r.db.Where("chat_id = ? AND timestamp BETWEEN ? AND ?", chatID, start, end).
		Order("timestamp ASC").Find(&messages).Error
	return messages, err
}

func (r *repository) GetMessageCount(chatID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.Model(&entities.Message{}).Where("chat_id = ?", chatID).Count(&count).Error
	return count, err
}

func (r *repository) CreateContact(contact *entities.Contact) error {
	return r.db.Create(contact).Error
}

func (r *repository) GetContactByNameAndPlatform(userID uuid.UUID, name string, platform entities.ChatPlatform) (*entities.Contact, error) {
	var contact entities.Contact
	err := r.db.Where("user_id = ? AND name = ? AND platform = ?", userID, name, platform).First(&contact).Error
	return &contact, err
}

func (r *repository) GetContactsByUserID(userID uuid.UUID) ([]entities.Contact, error) {
	var contacts []entities.Contact
	err := r.db.Where("user_id = ?", userID).Find(&contacts).Error
	return contacts, err
}

func (r *repository) GetContactByID(contactID uuid.UUID) (*entities.Contact, error) {
	var contact entities.Contact
	err := r.db.Where("id = ?", contactID).First(&contact).Error
	if err != nil {
		return nil, err
	}
	return &contact, nil
}

func (r *repository) UpdateContact(contact *entities.Contact) error {
	return r.db.Save(contact).Error
}

func (r *repository) AddParticipantToChat(chatID, contactID uuid.UUID) error {
	var chat entities.Chat
	var contact entities.Contact

	if err := r.db.First(&chat, "id = ?", chatID).Error; err != nil {
		return err
	}
	if err := r.db.First(&contact, "id = ?", contactID).Error; err != nil {
		return err
	}

	return r.db.Model(&chat).Association("Participants").Append(&contact)
}

func (r *repository) GetChatParticipants(chatID uuid.UUID) ([]entities.Contact, error) {
	var contacts []entities.Contact
	err := r.db.Table("contacts").
		Joins("JOIN chat_participants ON contacts.id = chat_participants.contact_id").
		Where("chat_participants.chat_id = ?", chatID).Find(&contacts).Error
	return contacts, err
}

func (r *repository) GetChatAnalysisByID(id uuid.UUID) (*entities.ChatAnalysis, error) {
	var analysis entities.ChatAnalysis
	err := r.db.Preload("User").Preload("Chat").First(&analysis, "id = ?", id).Error
	return &analysis, err
}

func (r *repository) CreateChatAnalysis(analysis *entities.ChatAnalysis) error {
	return r.db.Create(analysis).Error
}

func (r *repository) GetAnalysesByUserID(userID uuid.UUID, analysisType entities.AnalysisType) ([]entities.ChatAnalysis, error) {
	var analyses []entities.ChatAnalysis
	query := r.db.Where("user_id = ?", userID)
	if analysisType != "" {
		query = query.Where("analysis_type = ?", analysisType)
	}
	err := query.Order("created_at DESC").Find(&analyses).Error
	return analyses, err
}

func (r *repository) GetUserMessageStats(userID uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total messages
	var totalMessages int64
	r.db.Table("messages").
		Joins("JOIN chats ON messages.chat_id = chats.id").
		Where("chats.user_id = ?", userID).Count(&totalMessages)
	stats["total_messages"] = totalMessages

	// Total chats
	var totalChats int64
	r.db.Model(&entities.Chat{}).Where("user_id = ?", userID).Count(&totalChats)
	stats["total_chats"] = totalChats

	// Total contacts
	var totalContacts int64
	r.db.Model(&entities.Contact{}).Where("user_id = ?", userID).Count(&totalContacts)
	stats["total_contacts"] = totalContacts

	return stats, nil
}

func (r *repository) GetChatMessageStats(chatID uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Message count by type
	var messageTypes []struct {
		MessageType string
		Count       int64
	}
	r.db.Table("messages").
		Select("message_type, COUNT(*) as count").
		Where("chat_id = ?", chatID).
		Group("message_type").Scan(&messageTypes)
	stats["message_types"] = messageTypes

	// Messages by hour
	var hourlyStats []struct {
		Hour  int
		Count int64
	}
	r.db.Table("messages").
		Select("EXTRACT(hour FROM timestamp) as hour, COUNT(*) as count").
		Where("chat_id = ?", chatID).
		Group("hour").Order("hour").Scan(&hourlyStats)
	stats["hourly_distribution"] = hourlyStats

	return stats, nil
}
