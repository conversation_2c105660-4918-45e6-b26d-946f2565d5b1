package chat

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"regexp"
	"strings"
	"time"

	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	UploadChat(userID uuid.UUID, req *dtos.ChatUploadRequest) (*dtos.ChatUploadResponse, error)
	ProcessChatFile(userID uuid.UUID, file *multipart.FileHeader, platform entities.ChatPlatform, chatName string) (*dtos.ChatUploadResponse, error)
	GetUserChats(userID uuid.UUID) (*dtos.ChatListResponse, error)
	GetChatDetail(userID, chatID uuid.UUID) (*dtos.ChatDetailResponse, error)
	DeleteChat(userID, chatID uuid.UUID) error
	GetProcessingStatus(chatID uuid.UUID) (*dtos.ProcessingStatus, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) UploadChat(userID uuid.UUID, req *dtos.ChatUploadRequest) (*dtos.ChatUploadResponse, error) {
	platform := entities.ChatPlatform(req.Platform)
	return s.ProcessChatFile(userID, req.File, platform, req.ChatName)
}

func (s *service) ProcessChatFile(userID uuid.UUID, file *multipart.FileHeader, platform entities.ChatPlatform, chatName string) (*dtos.ChatUploadResponse, error) {
	startTime := time.Now()

	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer src.Close()

	// Create chat entity
	chat := &entities.Chat{
		UserID:   userID,
		Name:     chatName,
		Platform: platform,
		IsGroup:  false, // Will be determined during processing
	}

	if chat.Name == "" {
		chat.Name = fmt.Sprintf("%s Chat - %s", platform, time.Now().Format("2006-01-02"))
	}

	if err := s.repo.CreateChat(chat); err != nil {
		return nil, fmt.Errorf("failed to create chat: %w", err)
	}

	// Process messages based on platform
	var messages []entities.Message
	var contacts map[string]*entities.Contact = make(map[string]*entities.Contact)

	switch platform {
	case entities.WhatsApp:
		messages, contacts, err = s.processWhatsAppFile(src, userID, chat.ID)
	case entities.Telegram:
		messages, contacts, err = s.processTelegramFile(src, userID, chat.ID)
	case entities.Discord:
		messages, contacts, err = s.processDiscordFile(src, userID, chat.ID)
	default:
		messages, contacts, err = s.processGenericTextFile(src, userID, chat.ID)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to process %s file: %w", platform, err)
	}

	// Save contacts
	for _, contact := range contacts {
		if err := s.repo.CreateContact(contact); err != nil {
			// Contact might already exist, try to get existing one
			existing, getErr := s.repo.GetContactByNameAndPlatform(userID, contact.Name, platform)
			if getErr == nil {
				contact.ID = existing.ID
			}
		}
		// Add contact as participant
		s.repo.AddParticipantToChat(chat.ID, contact.ID)
	}

	// Save messages in batches
	if len(messages) > 0 {
		if err := s.repo.CreateMessages(messages); err != nil {
			return nil, fmt.Errorf("failed to save messages: %w", err)
		}

		// Update chat statistics
		chat.TotalMessages = len(messages)
		if len(messages) > 0 {
			chat.FirstMessageAt = messages[0].Timestamp
			chat.LastMessageAt = messages[len(messages)-1].Timestamp
		}
		chat.IsGroup = len(contacts) > 1

		if err := s.repo.UpdateChat(chat); err != nil {
			return nil, fmt.Errorf("failed to update chat stats: %w", err)
		}
	}

	processingTime := time.Since(startTime)

	return &dtos.ChatUploadResponse{
		ChatID:      chat.ID,
		Message:     "Chat uploaded and processed successfully",
		ProcessedAt: time.Now(),
		Stats: dtos.ChatStats{
			TotalMessages:    len(messages),
			TotalContacts:    len(contacts),
			ProcessingTimeMs: processingTime.Milliseconds(),
			DateRange: dtos.DateRange{
				Start: chat.FirstMessageAt,
				End:   chat.LastMessageAt,
			},
		},
	}, nil
}

func (s *service) processWhatsAppFile(src io.Reader, userID, chatID uuid.UUID) ([]entities.Message, map[string]*entities.Contact, error) {
	scanner := bufio.NewScanner(src)
	messages := []entities.Message{}
	contacts := make(map[string]*entities.Contact)

	// WhatsApp export formats:
	// English: [DD/MM/YYYY, HH:MM:SS] Contact Name: Message
	// Turkish: [D.MM.YYYY, HH:MM:SS] Contact Name: Message
	whatsappRegexSlash := regexp.MustCompile(`^\[(\d{1,2}/\d{1,2}/\d{4}), (\d{1,2}:\d{2}:\d{2})\] ([^:]+): (.*)$`)
	whatsappRegexDot := regexp.MustCompile(`^\[(\d{1,2}\.\d{1,2}\.\d{4}), (\d{1,2}:\d{2}:\d{2})\] ([^:]+): (.*)$`)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		// Skip system messages
		if strings.Contains(line, "\u200e") || // Left-to-right mark
			strings.Contains(line, "Messages and calls are end-to-end encrypted") ||
			strings.Contains(line, "is a contact") ||
			strings.Contains(line, "Voice call") {
			continue
		}

		var matches []string
		var dateStr, timeStr, senderName, content string
		var timestamp time.Time
		var err error

		// Try slash format first (English)
		matches = whatsappRegexSlash.FindStringSubmatch(line)
		if len(matches) == 5 {
			dateStr = matches[1]
			timeStr = matches[2]
			senderName = strings.TrimSpace(matches[3])
			content = matches[4]

			// Parse timestamp (DD/MM/YYYY format)
			timestamp, err = time.Parse("2/1/2006, 15:04:05", dateStr+", "+timeStr)
		} else {
			// Try dot format (Turkish)
			matches = whatsappRegexDot.FindStringSubmatch(line)
			if len(matches) == 5 {
				dateStr = matches[1]
				timeStr = matches[2]
				senderName = strings.TrimSpace(matches[3])
				content = matches[4]

				// Parse timestamp (DD.MM.YYYY format)
				timestamp, err = time.Parse("2.01.2006, 15:04:05", dateStr+", "+timeStr)
			} else {
				continue // Skip malformed lines
			}
		}

		if err != nil {
			continue // Skip lines with invalid timestamps
		}

		// Get or create contact
		contact := s.getOrCreateContact(contacts, senderName, userID, entities.WhatsApp)

		// Create message
		message := entities.Message{
			ChatID:      chatID,
			ContactID:   &contact.ID,
			Content:     content,
			MessageType: entities.TextMessage,
			Timestamp:   timestamp,
			IsFromUser:  false, // Will be determined later
			WordCount:   len(strings.Fields(content)),
			CharCount:   len(content),
		}

		// Basic emoji detection
		emojiRegex := regexp.MustCompile(`[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]`)
		if emojiRegex.MatchString(content) {
			message.HasEmoji = true
			message.EmojiCount = len(emojiRegex.FindAllString(content, -1))
		}

		messages = append(messages, message)
	}

	return messages, contacts, scanner.Err()
}

func (s *service) processTelegramFile(src io.Reader, userID, chatID uuid.UUID) ([]entities.Message, map[string]*entities.Contact, error) {
	var telegramData struct {
		Messages []dtos.TelegramMessage `json:"messages"`
	}

	decoder := json.NewDecoder(src)
	if err := decoder.Decode(&telegramData); err != nil {
		return nil, nil, fmt.Errorf("failed to parse Telegram JSON: %w", err)
	}

	messages := []entities.Message{}
	contacts := make(map[string]*entities.Contact)

	for _, tgMsg := range telegramData.Messages {
		if tgMsg.Text == "" {
			continue
		}

		contact := s.getOrCreateContact(contacts, tgMsg.From, userID, entities.Telegram)

		message := entities.Message{
			ChatID:      chatID,
			ContactID:   &contact.ID,
			Content:     tgMsg.Text,
			MessageType: entities.TextMessage,
			Timestamp:   tgMsg.Date,
			IsFromUser:  false,
			WordCount:   len(strings.Fields(tgMsg.Text)),
			CharCount:   len(tgMsg.Text),
		}

		messages = append(messages, message)
	}

	return messages, contacts, nil
}

func (s *service) processDiscordFile(src io.Reader, userID, chatID uuid.UUID) ([]entities.Message, map[string]*entities.Contact, error) {
	var discordMessages []dtos.DiscordMessage

	decoder := json.NewDecoder(src)
	if err := decoder.Decode(&discordMessages); err != nil {
		return nil, nil, fmt.Errorf("failed to parse Discord JSON: %w", err)
	}

	messages := []entities.Message{}
	contacts := make(map[string]*entities.Contact)

	for _, discordMsg := range discordMessages {
		if discordMsg.Content == "" {
			continue
		}

		senderName := discordMsg.Author.Username
		if discordMsg.Author.Name != "" {
			senderName = discordMsg.Author.Name
		}

		contact := s.getOrCreateContact(contacts, senderName, userID, entities.Discord)

		message := entities.Message{
			ChatID:      chatID,
			ContactID:   &contact.ID,
			Content:     discordMsg.Content,
			MessageType: entities.TextMessage,
			Timestamp:   discordMsg.Timestamp,
			IsFromUser:  false,
			WordCount:   len(strings.Fields(discordMsg.Content)),
			CharCount:   len(discordMsg.Content),
		}

		messages = append(messages, message)
	}

	return messages, contacts, nil
}

func (s *service) processGenericTextFile(src io.Reader, userID, chatID uuid.UUID) ([]entities.Message, map[string]*entities.Contact, error) {
	scanner := bufio.NewScanner(src)
	messages := []entities.Message{}
	contacts := make(map[string]*entities.Contact)

	lineNumber := 0
	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		// Generic format: assume each line is a message
		contact := s.getOrCreateContact(contacts, "Unknown Contact", userID, entities.Other)

		message := entities.Message{
			ChatID:      chatID,
			ContactID:   &contact.ID,
			Content:     line,
			MessageType: entities.TextMessage,
			Timestamp:   time.Now().Add(-time.Duration(lineNumber) * time.Minute), // Fake timestamps
			IsFromUser:  false,
			WordCount:   len(strings.Fields(line)),
			CharCount:   len(line),
		}

		messages = append(messages, message)
	}

	return messages, contacts, scanner.Err()
}

func (s *service) getOrCreateContact(contacts map[string]*entities.Contact, name string, userID uuid.UUID, platform entities.ChatPlatform) *entities.Contact {
	if contact, exists := contacts[name]; exists {
		return contact
	}

	contact := &entities.Contact{
		UserID:   userID,
		Name:     name,
		Platform: platform,
	}
	contacts[name] = contact
	return contact
}

func (s *service) GetUserChats(userID uuid.UUID) (*dtos.ChatListResponse, error) {
	chats, err := s.repo.GetChatsByUserID(userID)
	if err != nil {
		return nil, err
	}

	chatSummaries := make([]dtos.ChatSummary, len(chats))
	for i, chat := range chats {
		chatSummaries[i] = dtos.ChatSummary{
			ID:               chat.ID,
			Name:             chat.Name,
			Platform:         chat.Platform,
			IsGroup:          chat.IsGroup,
			TotalMessages:    chat.TotalMessages,
			FirstMessageAt:   chat.FirstMessageAt,
			LastMessageAt:    chat.LastMessageAt,
			IsAnalyzed:       chat.IsAnalyzed,
			ParticipantCount: 0, // Will be calculated separately if needed
			CreatedAt:        chat.CreatedAt,
		}
	}

	return &dtos.ChatListResponse{
		Chats: chatSummaries,
		Total: len(chatSummaries),
	}, nil
}

func (s *service) GetChatDetail(userID, chatID uuid.UUID) (*dtos.ChatDetailResponse, error) {
	chat, err := s.repo.GetChatByID(chatID)
	if err != nil {
		return nil, err
	}

	if chat.UserID != userID {
		return nil, fmt.Errorf("unauthorized access to chat")
	}

	// Get recent messages
	messages, err := s.repo.GetMessagesByChatID(chatID, 50, 0)
	if err != nil {
		return nil, err
	}

	// Convert to DTOs
	messageSummaries := make([]dtos.MessageSummary, len(messages))
	for i, msg := range messages {
		contactName := ""
		if msg.ContactID != nil {
			// Get contact info separately since we removed foreign keys
			contact, err := s.repo.GetContactByID(*msg.ContactID)
			if err == nil {
				contactName = contact.Name
			}
		}

		messageSummaries[i] = dtos.MessageSummary{
			ID:          msg.ID,
			Content:     msg.Content,
			MessageType: msg.MessageType,
			Timestamp:   msg.Timestamp,
			IsFromUser:  msg.IsFromUser,
			ContactName: contactName,
			WordCount:   msg.WordCount,
			HasEmoji:    msg.HasEmoji,
		}
	}

	// Get participants separately since we removed many-to-many relationship
	participants, err := s.repo.GetChatParticipants(chatID)
	if err != nil {
		participants = []entities.Contact{} // Empty if error
	}

	participantSummaries := make([]dtos.ContactSummary, len(participants))
	for i, participant := range participants {
		participantSummaries[i] = dtos.ContactSummary{
			ID:                participant.ID,
			Name:              participant.Name,
			Platform:          participant.Platform,
			TotalMessages:     participant.TotalMessages,
			MessagesSent:      participant.MessagesSent,
			MessagesReceived:  participant.MessagesReceived,
			RelationshipScore: participant.RelationshipScore,
		}
	}

	return &dtos.ChatDetailResponse{
		Chat: dtos.ChatSummary{
			ID:               chat.ID,
			Name:             chat.Name,
			Platform:         chat.Platform,
			IsGroup:          chat.IsGroup,
			TotalMessages:    chat.TotalMessages,
			FirstMessageAt:   chat.FirstMessageAt,
			LastMessageAt:    chat.LastMessageAt,
			IsAnalyzed:       chat.IsAnalyzed,
			ParticipantCount: len(participants),
			CreatedAt:        chat.CreatedAt,
		},
		Participants:   participantSummaries,
		RecentMessages: messageSummaries,
	}, nil
}

func (s *service) DeleteChat(userID, chatID uuid.UUID) error {
	chat, err := s.repo.GetChatByID(chatID)
	if err != nil {
		return err
	}

	if chat.UserID != userID {
		return fmt.Errorf("unauthorized access to chat")
	}

	return s.repo.DeleteChat(chatID)
}

func (s *service) GetProcessingStatus(chatID uuid.UUID) (*dtos.ProcessingStatus, error) {
	chat, err := s.repo.GetChatByID(chatID)
	if err != nil {
		return nil, err
	}

	status := "completed"
	if !chat.IsAnalyzed {
		status = "processing"
	}

	return &dtos.ProcessingStatus{
		ChatID:    chatID,
		Status:    status,
		Progress:  100,
		Message:   "Chat processing completed",
		StartedAt: chat.CreatedAt,
		UpdatedAt: chat.UpdatedAt,
	}, nil
}
