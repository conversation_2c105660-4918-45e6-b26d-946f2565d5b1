package auth

import (
	"errors"

	"github.com/go-base-project-structure/pkg/consts"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/go-base-project-structure/pkg/sayelog"
	"github.com/go-base-project-structure/pkg/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateUser(dtos dtos.CreateUserReqDto) error
	GetUserByUserName(username string) (entities.User, error)
	GetUserByEmail(email string) (*entities.User, error)
	GetUserByID(id uuid.UUID) (*entities.User, error)
	UserExists(email, username string) (bool, error)
	UpdateUser(user *entities.User) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreateUser(dtos dtos.CreateUserReqDto) error {
	var user = entities.User{
		Username: dtos.Username,
		Password: utils.Bcrypt(dtos.Password),
		Name:     dtos.Name,
		Email:    dtos.Email,
	}
	user.ID = uuid.New()

	err := r.db.Create(&user).Error
	if err != nil {
		sayelog.CreateLog(&entities.Log{
			Title:   "Create User Error",
			Message: "Create User Error: " + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return errors.New(consts.CreateUserFailed)
	}
	return nil
}

func (r *repository) GetUserByUserName(username string) (entities.User, error) {
	var user entities.User
	err := r.db.Where("username = ?", username).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, err
}

func (r *repository) GetUserByEmail(email string) (*entities.User, error) {
	var user entities.User
	err := r.db.Where("email = ?", email).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetUserByID(id uuid.UUID) (*entities.User, error) {
	var user entities.User
	err := r.db.Where("id = ?", id).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

func (r *repository) UserExists(email, username string) (bool, error) {
	var count int64
	err := r.db.Model(&entities.User{}).
		Where("email = ? OR username = ?", email, username).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *repository) UpdateUser(user *entities.User) error {
	return r.db.Save(user).Error
}
