package auth

import (
	"errors"
	"fmt"
	"time"

	"github.com/go-base-project-structure/pkg/config"
	"github.com/go-base-project-structure/pkg/consts"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/go-base-project-structure/pkg/sayelog"
	"github.com/go-base-project-structure/pkg/utils"
	"github.com/google/uuid"
)

type Service interface {
	Login(payload *dtos.AuthenticationRequest) (*dtos.AuthenticationResponse, error)
	Register(payload dtos.CreateUserReqDto) (*dtos.CreateUserRespDto, error)
	GetUserProfile(userID uuid.UUID) (*dtos.UserProfileResponse, error)
	UpdateProfile(userID uuid.UUID, req dtos.UpdateProfileRequest) (*dtos.UpdateProfileResponse, error)
	ChangePassword(userID uuid.UUID, req dtos.ChangePasswordRequest) (*dtos.ChangePasswordResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Register(payload dtos.CreateUserReqDto) (*dtos.CreateUserRespDto, error) {
	// Check if user already exists
	exists, err := s.repository.UserExists(payload.Email, payload.Username)
	if err != nil {
		return nil, fmt.Errorf("failed to check user existence: %w", err)
	}
	if exists {
		return nil, errors.New("user with this email or username already exists")
	}

	// Create user
	err = s.repository.CreateUser(payload)
	if err != nil {
		return nil, err
	}

	// Get created user to return ID
	user, err := s.repository.GetUserByEmail(payload.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to get created user: %w", err)
	}

	return &dtos.CreateUserRespDto{
		Message: "User created successfully",
		UserID:  user.ID,
	}, nil
}

func (s *service) Login(payload *dtos.AuthenticationRequest) (*dtos.AuthenticationResponse, error) {
	var user entities.User
	var err error

	// Login with email or username
	if payload.Email != "" {
		userPtr, err := s.repository.GetUserByEmail(payload.Email)
		if err != nil {
			return nil, errors.New("invalid credentials")
		}
		user = *userPtr
	} else if payload.Username != "" {
		user, err = s.repository.GetUserByUserName(payload.Username)
		if err != nil {
			return nil, errors.New("invalid credentials")
		}
	} else {
		return nil, errors.New("email or username is required")
	}

	// Check password
	if !utils.Compare(user.Password, payload.Password) {
		sayelog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: "Invalid password attempt",
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return nil, errors.New("invalid credentials")
	}

	// Generate JWT token
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Username, user.ID.String())
	if err != nil {
		sayelog.CreateLog(&entities.Log{
			Title:   "JWT Generation Failed",
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return nil, errors.New("failed to generate token")
	}

	// Log successful login
	sayelog.CreateLog(&entities.Log{
		Title:   "User Login",
		Message: fmt.Sprintf("User %s logged in successfully", user.Username),
		Type:    "info",
		Entity:  "user",
		UserID:  user.ID,
	})

	return &dtos.AuthenticationResponse{
		Token:       token,
		Expires:     time.Now().Add(time.Duration(config.ReadValue().App.JwtExpire) * time.Hour),
		IsSucceeded: true,
		User: dtos.UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
			Email:    user.Email,
		},
	}, nil
}

func (s *service) GetUserProfile(userID uuid.UUID) (*dtos.UserProfileResponse, error) {
	user, err := s.repository.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	// Calculate user stats (simplified)
	stats := dtos.UserStats{
		TotalChats:    0, // Would be calculated from chat repository
		TotalMessages: 0, // Would be calculated from message repository
		TotalContacts: 0, // Would be calculated from contact repository
		JoinedDays:    int(time.Since(user.CreatedAt).Hours() / 24),
	}

	return &dtos.UserProfileResponse{
		User: dtos.UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
			Email:    user.Email,
		},
		Stats: stats,
	}, nil
}

func (s *service) UpdateProfile(userID uuid.UUID, req dtos.UpdateProfileRequest) (*dtos.UpdateProfileResponse, error) {
	user, err := s.repository.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.Name != "" {
		user.Name = req.Name
	}
	if req.Email != "" {
		// Check if email is already taken by another user
		existingUser, _ := s.repository.GetUserByEmail(req.Email)
		if existingUser != nil && existingUser.ID != userID {
			return nil, errors.New("email is already taken")
		}
		user.Email = req.Email
	}

	// Save updated user
	err = s.repository.UpdateUser(user)
	if err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return &dtos.UpdateProfileResponse{
		Message: "Profile updated successfully",
		User: dtos.UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
			Email:    user.Email,
		},
	}, nil
}

func (s *service) ChangePassword(userID uuid.UUID, req dtos.ChangePasswordRequest) (*dtos.ChangePasswordResponse, error) {
	user, err := s.repository.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	// Verify current password
	if !utils.Compare(user.Password, req.CurrentPassword) {
		return nil, errors.New("current password is incorrect")
	}

	// Hash new password
	user.Password = utils.Bcrypt(req.NewPassword)

	// Save updated user
	err = s.repository.UpdateUser(user)
	if err != nil {
		return nil, fmt.Errorf("failed to update password: %w", err)
	}

	// Log password change
	sayelog.CreateLog(&entities.Log{
		Title:   "Password Changed",
		Message: fmt.Sprintf("User %s changed password", user.Username),
		Type:    "info",
		Entity:  "user",
		UserID:  user.ID,
	})

	return &dtos.ChangePasswordResponse{
		Message: "Password changed successfully",
	}, nil
}
