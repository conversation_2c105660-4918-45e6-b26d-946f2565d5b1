package server

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/go-base-project-structure/app/api/routes"
	"github.com/go-base-project-structure/pkg/config"
	"github.com/go-base-project-structure/pkg/database"
	"github.com/go-base-project-structure/pkg/domains/analytics"
	"github.com/go-base-project-structure/pkg/domains/auth"
	"github.com/go-base-project-structure/pkg/domains/chat"
	"github.com/go-base-project-structure/pkg/domains/social"
	"github.com/go-base-project-structure/pkg/domains/version"
	"github.com/go-base-project-structure/pkg/domains/wrapped"
	"github.com/go-base-project-structure/pkg/embed"
	"github.com/go-base-project-structure/pkg/middleware"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/swaggo/swag/example/basic/docs"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())

	//app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()

	// -----> Routes Start
	api := app.Group("/api/v1")

	version_repo := version.NewRepo(db)
	auth_repo := auth.NewRepo(db)
	chat_repo := chat.NewRepository(db)
	analytics_repo := analytics.NewRepository(db)

	// Initialize services
	version_service := version.NewService(version_repo)
	auth_service := auth.NewService(auth_repo)
	chat_service := chat.NewService(chat_repo)
	analytics_service := analytics.NewService(analytics_repo, chat_repo)
	social_service := social.NewService(chat_repo, analytics_repo)
	wrapped_service := wrapped.NewService(chat_repo, analytics_repo, analytics_service)

	// Initialize handlers
	auth_handler := routes.NewAuthHandler(auth_service)
	chat_handler := routes.NewChatHandler(chat_service)
	analytics_handler := routes.NewAnalyticsHandler(analytics_service, social_service, wrapped_service)

	// Register routes
	routes.VersionRoutes(api, version_service)

	// Auth routes (public + protected)
	auth_handler.RegisterRoutes(api)

	// Protected routes with JWT authentication
	chat_handler.RegisterRoutes(api)
	analytics_handler.RegisterRoutes(api)

	// Routes End <-----

	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "docs/index.html")
	})

	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "saye-dev"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "saye-dev"
	}

	docs.SwaggerInfo.Host = config.InitConfig().App.BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")
	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	app.GET("/assets/*filepath", func(c *gin.Context) {
		c.FileFromFS(path.Join("/dist/", c.Request.URL.Path), http.FS(embed.StaticsFS()))
	})
	app.Any("/", func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})
	app.GET("/robots.txt", func(c *gin.Context) {
		c.FileFromFS("dist/robots.txt", http.FS(embed.StaticsFS()))
	})
	app.GET("/favicon.ico", func(c *gin.Context) {
		c.FileFromFS("dist/favicon.ico", http.FS(embed.StaticsFS()))
	})
	app.NoRoute(func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})

	fmt.Println("Server is running on port " + appc.Port)
	app.Run(net.JoinHostPort(appc.Host, appc.Port))
}
