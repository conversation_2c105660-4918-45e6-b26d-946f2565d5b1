#!/bin/bash

# ChatMuse Full Flow Test Script
# This script demonstrates the complete ChatMuse workflow

echo "🚀 ChatMuse Full Flow Test Starting..."
echo "========================================"

BASE_URL="http://localhost:8000/api/v1"
USER_TOKEN="" # Will be obtained from login

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Function to make API calls with error handling
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    print_step "🔄 $description"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL$endpoint" \
            -H "Authorization: Bearer $USER_TOKEN" \
            -H "Content-Type: application/json")
    elif [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL$endpoint" \
            -H "Authorization: Bearer $USER_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL$endpoint" \
            -H "Authorization: Bearer $USER_TOKEN")
    fi
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        print_success "$description completed"
        echo "$body" | jq . 2>/dev/null || echo "$body"
        echo ""
        return 0
    else
        print_error "$description failed (HTTP $http_code)"
        echo "$body"
        echo ""
        return 1
    fi
}

# Function to upload file
upload_file() {
    local file_path=$1
    local platform=$2
    local chat_name=$3
    local description=$4
    
    print_step "📤 $description"
    
    if [ ! -f "$file_path" ]; then
        print_error "File not found: $file_path"
        return 1
    fi
    
    response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/chats/upload" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -F "file=@$file_path" \
        -F "platform=$platform" \
        -F "chat_name=$chat_name")
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        print_success "$description completed"
        echo "$body" | jq . 2>/dev/null || echo "$body"
        
        # Extract chat_id for later use
        if command -v jq >/dev/null 2>&1; then
            CHAT_ID=$(echo "$body" | jq -r '.chat_id')
            print_info "Chat ID: $CHAT_ID"
        fi
        echo ""
        return 0
    else
        print_error "$description failed (HTTP $http_code)"
        echo "$body"
        echo ""
        return 1
    fi
}

# Check if jq is installed
if ! command -v jq >/dev/null 2>&1; then
    print_info "jq is not installed. JSON responses will be shown as raw text."
    print_info "Install jq for better formatting: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    echo ""
fi

# Step 1: Health Check
print_step "1️⃣ Health Check"
api_call "GET" "/version" "" "Checking API health"

# Step 2: Register New User (Optional)
print_step "2️⃣ Register New User (Optional)"
REGISTER_DATA='{"username":"testuser_'$(date +%s)'","email":"test'$(date +%s)'@chatmuse.com","password":"password123","name":"Test User"}'
echo "Registering new user..."
curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d "$REGISTER_DATA" | jq . 2>/dev/null || echo "Registration response received"

# Step 3: Login and Get Token
print_step "3️⃣ Login and Get Real Token"
LOGIN_DATA='{"email":"<EMAIL>","password":"password123"}'
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "$LOGIN_DATA")

echo "$LOGIN_RESPONSE" | jq . 2>/dev/null || echo "$LOGIN_RESPONSE"

# Extract token if jq is available
if command -v jq >/dev/null 2>&1; then
    REAL_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.token // empty')
    if [ -n "$REAL_TOKEN" ] && [ "$REAL_TOKEN" != "null" ]; then
        USER_TOKEN="$REAL_TOKEN"
        print_success "JWT token obtained and will be used for subsequent requests"
        echo "Token: ${REAL_TOKEN:0:50}..."
    else
        print_error "Login failed or no token received. Cannot proceed without authentication."
        exit 1
    fi
else
    print_error "jq not available, cannot extract token. Please install jq or manually set USER_TOKEN."
    exit 1
fi
echo ""

# Step 4: Check if dummy data exists
print_step "4️⃣ Checking Dummy Data"
api_call "GET" "/chats" "" "Getting existing chats"

# Step 5: Upload WhatsApp Chat
print_step "5️⃣ Upload WhatsApp Family Chat"
upload_file "sample_data/whatsapp_family.txt" "whatsapp" "Family Group 2024" "Uploading WhatsApp family chat"

# Store the chat ID for analysis
WHATSAPP_CHAT_ID=$CHAT_ID

# Step 6: Upload Telegram Chat
print_step "6️⃣ Upload Telegram Friends Chat"
upload_file "sample_data/telegram_friends.json" "telegram" "College Friends Study Group" "Uploading Telegram friends chat"

# Store the chat ID for analysis
TELEGRAM_CHAT_ID=$CHAT_ID

# Step 7: List all chats
print_step "7️⃣ List All Chats"
api_call "GET" "/chats" "" "Getting all user chats"

# Step 6: Analyze WhatsApp Chat
if [ -n "$WHATSAPP_CHAT_ID" ] && [ "$WHATSAPP_CHAT_ID" != "null" ]; then
    print_step "6️⃣ Analyze WhatsApp Chat"
    api_call "POST" "/analytics/chats/$WHATSAPP_CHAT_ID/analyze" "" "Analyzing WhatsApp family chat"
    
    # Get chat insights
    print_step "📊 Get WhatsApp Chat Insights"
    api_call "GET" "/analytics/chats/$WHATSAPP_CHAT_ID/insights" "" "Getting WhatsApp chat insights"
else
    print_error "WhatsApp chat ID not available for analysis"
fi

# Step 7: Analyze Telegram Chat
if [ -n "$TELEGRAM_CHAT_ID" ] && [ "$TELEGRAM_CHAT_ID" != "null" ]; then
    print_step "7️⃣ Analyze Telegram Chat"
    api_call "POST" "/analytics/chats/$TELEGRAM_CHAT_ID/analyze" "" "Analyzing Telegram friends chat"
    
    # Get chat insights
    print_step "📊 Get Telegram Chat Insights"
    api_call "GET" "/analytics/chats/$TELEGRAM_CHAT_ID/insights" "" "Getting Telegram chat insights"
else
    print_error "Telegram chat ID not available for analysis"
fi

# Step 8: Generate 2024 Wrapped Summary
print_step "8️⃣ Generate 2024 Wrapped Summary"
api_call "POST" "/analytics/wrapped/2024" "" "Generating 2024 wrapped summary"

# Step 9: Get Wrapped History
print_step "9️⃣ Get Wrapped History"
api_call "GET" "/analytics/wrapped" "" "Getting wrapped summaries history"

# Step 10: Social Network Analysis
print_step "🔟 Social Network Analysis"
api_call "POST" "/analytics/social/analyze" "" "Analyzing social network"

# Step 11: Get Strongest Connections
print_step "1️⃣1️⃣ Get Strongest Connections"
api_call "GET" "/analytics/social/strongest?limit=5" "" "Getting strongest connections"

# Step 12: Get User Analytics
print_step "1️⃣2️⃣ Get User Analytics"
api_call "GET" "/analytics/user" "" "Getting user analytics"

# Step 13: Emoji Analysis
print_step "1️⃣3️⃣ Emoji Analysis"
if [ -n "$WHATSAPP_CHAT_ID" ] && [ "$WHATSAPP_CHAT_ID" != "null" ]; then
    api_call "GET" "/analytics/user/emojis?chat_id=$WHATSAPP_CHAT_ID" "" "Getting emoji analysis for WhatsApp chat"
else
    api_call "GET" "/analytics/user/emojis" "" "Getting overall emoji analysis"
fi

# Step 14: Word Analysis
print_step "1️⃣4️⃣ Word Analysis"
if [ -n "$TELEGRAM_CHAT_ID" ] && [ "$TELEGRAM_CHAT_ID" != "null" ]; then
    api_call "GET" "/analytics/user/words?chat_id=$TELEGRAM_CHAT_ID" "" "Getting word analysis for Telegram chat"
else
    api_call "GET" "/analytics/user/words" "" "Getting overall word analysis"
fi

# Step 15: Relationship Trends
print_step "1️⃣5️⃣ Relationship Trends"
api_call "GET" "/analytics/social/trends" "" "Getting relationship trends"

# Final Summary
echo ""
echo "========================================"
print_success "🎉 ChatMuse Full Flow Test Completed!"
echo ""
print_info "Summary of what was tested:"
echo "  ✅ API Health Check"
echo "  ✅ Chat Upload (WhatsApp & Telegram)"
echo "  ✅ Chat Listing"
echo "  ✅ Chat Analysis"
echo "  ✅ Chat Insights"
echo "  ✅ Wrapped Summary Generation"
echo "  ✅ Social Network Analysis"
echo "  ✅ Emoji & Word Analysis"
echo "  ✅ Relationship Analysis"
echo ""
print_info "🌐 Access Swagger Documentation: http://localhost:8000/docs"
print_info "📊 Access PgAdmin: http://localhost:5050"
print_info "🔍 Check application logs for detailed information"
echo ""
print_success "ChatMuse is ready for frontend development! 🚀"
