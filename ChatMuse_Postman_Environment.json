{"id": "chatmuse-environment", "name": "ChatMuse Development", "values": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "default", "enabled": true}, {"key": "auth_token", "value": "demo_token_123", "type": "default", "enabled": true}, {"key": "chat_id", "value": "", "type": "default", "enabled": true}, {"key": "contact_id", "value": "", "type": "default", "enabled": true}, {"key": "user_id", "value": "demo_user_id", "type": "default", "enabled": true}, {"key": "swagger_url", "value": "http://localhost:8000/docs", "type": "default", "enabled": true}, {"key": "pgadmin_url", "value": "http://localhost:5050", "type": "default", "enabled": true}, {"key": "database_host", "value": "localhost", "type": "default", "enabled": true}, {"key": "database_port", "value": "5433", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}